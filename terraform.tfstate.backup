{"version": 4, "terraform_version": "1.8.4", "serial": 187, "lineage": "9b58896f-6e9f-be61-6241-30442d7fbe39", "outputs": {"aws_load_balancer_controller_role_arn": {"value": "arn:aws:iam::************:role/aws-load-balancer-controller", "type": "string"}}, "resources": [{"mode": "data", "type": "aws_iam_policy_document", "name": "aws_load_balancer_controller_assume_role_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "*********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"sts:AssumeRoleWithWebIdentity\",\n      \"Principal\": {\n        \"Federated\": \"arn:aws:iam::************:oidc-provider/oidc.eks.ap-northeast-1.amazonaws.com/id/B3DE07E3C89D23D8EC6AA8DFD0272007\"\n      },\n      \"Condition\": {\n        \"StringEquals\": {\n          \"oidc.eks.ap-northeast-1.amazonaws.com/id/B3DE07E3C89D23D8EC6AA8DFD0272007:sub\": \"system:serviceaccount:kube-system:aws-load-balancer-controller\"\n        }\n      }\n    }\n  ]\n}", "override_policy_documents": null, "policy_id": null, "source_policy_documents": null, "statement": [{"actions": ["sts:AssumeRoleWithWebIdentity"], "condition": [{"test": "StringEquals", "values": ["system:serviceaccount:kube-system:aws-load-balancer-controller"], "variable": "oidc.eks.ap-northeast-1.amazonaws.com/id/B3DE07E3C89D23D8EC6AA8DFD0272007:sub"}], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["arn:aws:iam::************:oidc-provider/oidc.eks.ap-northeast-1.amazonaws.com/id/B3DE07E3C89D23D8EC6AA8DFD0272007"], "type": "Federated"}], "resources": [], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "tls_certificate", "name": "eks", "provider": "provider[\"registry.terraform.io/hashicorp/tls\"]", "instances": [{"schema_version": 0, "attributes": {"certificates": [{"cert_pem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIEdTCCA12gAwIBAgIJAKcOSkw0grd/MA0GCSqGSIb3DQEBCwUAMGgxCzAJBgNV\nBAYTAlVTMSUwIwYDVQQKExxTdGFyZmllbGQgVGVjaG5vbG9naWVzLCBJbmMuMTIw\nMAYDVQQLEylTdGFyZmllbGQgQ2xhc3MgMiBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0\neTAeFw0wOTA5MDIwMDAwMDBaFw0zNDA2MjgxNzM5MTZaMIGYMQswCQYDVQQGEwJV\nUzEQMA4GA1UECBMHQXJpem9uYTETMBEGA1UEBxMKU2NvdHRzZGFsZTElMCMGA1UE\nChMcU3RhcmZpZWxkIFRlY2hub2xvZ2llcywgSW5jLjE7MDkGA1UEAxMyU3RhcmZp\nZWxkIFNlcnZpY2VzIFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5IC0gRzIwggEi\nMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDVDDrEKvlO4vW+GZdfjohTsR8/\ny8+fIBNtKTrID30892t2OGPZNmCom15cAICyL1l/9of5JUOG52kbUpqQ4XHj2C0N\nTm/2yEnZtvMaVq4rtnQU68/7JuMauh2WLmo7WJSJR1b/JaCTcFOD2oR0FMNnngRo\nOt+OQFodSk7PQ5E751bWAHDLUu57fa4657wx+UX2wmDPE1kCK4DMNEffud6QZW0C\nzyyRpqbn3oUYSXxmTqM6bam17jQuug0DuDPfR+uxa40l2ZvOgdFFRjKWcIfeAg5J\nQ4W2bHO7ZOphQazJ1FTfhy/HIrImzJ9ZVGif/L4qL8RVHHVAYBeFAlU5i38FAgMB\nAAGjgfAwge0wDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAYYwHQYDVR0O\nBBYEFJxfAN+qAdcwKziIorhtSpzyEZGDMB8GA1UdIwQYMBaAFL9ft9HO3R+G9FtV\nrNzXEMIOqYjnME8GCCsGAQUFBwEBBEMwQTAcBggrBgEFBQcwAYYQaHR0cDovL28u\nc3MyLnVzLzAhBggrBgEFBQcwAoYVaHR0cDovL3guc3MyLnVzL3guY2VyMCYGA1Ud\nHwQfMB0wG6AZoBeGFWh0dHA6Ly9zLnNzMi51cy9yLmNybDARBgNVHSAECjAIMAYG\nBFUdIAAwDQYJKoZIhvcNAQELBQADggEBACMd44pXyn3pF3lM8R5V/cxTbj5HD9/G\nVfKyBDbtgB9TxF00KGu+x1X8Z+rLP3+QsjPNG1gQggL4+C/1E2DUBc7xgQjB3ad1\nl08YuW3e95ORCLp+QCztweq7dp4zBncdDQh/U90bZKuCJ/Fp1U1ervShw3WnWEQt\n8jxwmKy6abaVd38PMV4s/KCHOkdp8Hlf9BRUpJVeEXgSYCfOn8J3/yNTd126/+pZ\n59vPr5KW7ySaNRB6nJHGDn2Z9j8Z3/VyVOEVqQdZe4O/Ui5GjLIAZHYcSNPYeehu\nVsyuLAOQ1xk4meTKCRlb/weWsKh/NEnfVqn3sF/tM+2MR7cwA130A4w=\n-----END CERTIFICATE-----\n", "is_ca": true, "issuer": "OU=Starfield Class 2 Certification Authority,O=Starfield Technologies\\, Inc.,C=US", "not_after": "2034-06-28T17:39:16Z", "not_before": "2009-09-02T00:00:00Z", "public_key_algorithm": "RSA", "serial_number": "12037640545166866303", "sha1_fingerprint": "9e99a48a9960b14926bb7f3b02e22da2b0ab7280", "signature_algorithm": "SHA256-RSA", "subject": "CN=Starfield Services Root Certificate Authority - G2,O=Starfield Technologies\\, Inc.,L=Scottsdale,ST=Arizona,C=US", "version": 3}, {"cert_pem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIEkjCCA3qgAwIBAgITBn+USionzfP6wq4rAfkI7rnExjANBgkqhkiG9w0BAQsF\nADCBmDELMAkGA1UEBhMCVVMxEDAOBgNVBAgTB0FyaXpvbmExEzARBgNVBAcTClNj\nb3R0c2RhbGUxJTAjBgNVBAoTHFN0YXJmaWVsZCBUZWNobm9sb2dpZXMsIEluYy4x\nOzA5BgNVBAMTMlN0YXJmaWVsZCBTZXJ2aWNlcyBSb290IENlcnRpZmljYXRlIEF1\ndGhvcml0eSAtIEcyMB4XDTE1MDUyNTEyMDAwMFoXDTM3MTIzMTAxMDAwMFowOTEL\nMAkGA1UEBhMCVVMxDzANBgNVBAoTBkFtYXpvbjEZMBcGA1UEAxMQQW1hem9uIFJv\nb3QgQ0EgMTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALJ4gHHKeNXj\nca9HgFB0fW7Y14h29Jlo91ghYPl0hAEvrAIthtOgQ3pOsqTQNroBvo3bSMgHFzZM\n9O6II8c+6zf1tRn4SWiw3te5djgdYZ6k/oI2peVKVuRF4fn9tBb6dNqcmzU5L/qw\nIFAGbHrQgLKm+a/sRxmPUDgH3KKHOVj4utWp+UhnMJbulHheb4mjUcAwhmahRWa6\nVOujw5H5SNz/0egwLX0tdHA114gk957EWW67c4cX8jJGKLhD+rcdqsq08p8kDi1L\n93FcXmn/6pUCyziKrlA4b9v7LWIbxcceVOF34GfID5yHI9Y/QCB/IIDEgEw+OyQm\njgSubJrIqg0CAwEAAaOCATEwggEtMA8GA1UdEwEB/wQFMAMBAf8wDgYDVR0PAQH/\nBAQDAgGGMB0GA1UdDgQWBBSEGMyFNOy8DJSULghZnMeyEE4KCDAfBgNVHSMEGDAW\ngBScXwDfqgHXMCs4iKK4bUqc8hGRgzB4BggrBgEFBQcBAQRsMGowLgYIKwYBBQUH\nMAGGImh0dHA6Ly9vY3NwLnJvb3RnMi5hbWF6b250cnVzdC5jb20wOAYIKwYBBQUH\nMAKGLGh0dHA6Ly9jcnQucm9vdGcyLmFtYXpvbnRydXN0LmNvbS9yb290ZzIuY2Vy\nMD0GA1UdHwQ2MDQwMqAwoC6GLGh0dHA6Ly9jcmwucm9vdGcyLmFtYXpvbnRydXN0\nLmNvbS9yb290ZzIuY3JsMBEGA1UdIAQKMAgwBgYEVR0gADANBgkqhkiG9w0BAQsF\nAAOCAQEAYjdCXLwQtT6LLOkMm2xF4gcAevnFWAu5CIw+7bMlPLVvUOTNNWqnkzSW\nMiGpSESrnO09tKpzbeR/FoCJbM8oAxiDR3mjEH4wW6w7sGDgd9QIpuEdfF7Au/ma\neyKdpwAJfqxGF4PcnCZXmTA5YpaP7dreqsXMGz7KQ2hsVxa81Q4gLv7/wmpdLqBK\nbRRYh5TmOTFffHPLkIhqhBGWJ6bt2YFGpn6jcgAKUj6DiAdjd4lpFw85hdKrCEVN\n0FE6/V1dN2RMfjCyVSRCnTawXZwXgWHxyvkQAiSr6w10kY17RSlQOYiypok1JR4U\nakcjMS9cmvqtmg5iUaQqqcT5NJ0hGA==\n-----END CERTIFICATE-----\n", "is_ca": true, "issuer": "CN=Starfield Services Root Certificate Authority - G2,O=Starfield Technologies\\, Inc.,L=Scottsdale,ST=Arizona,C=US", "not_after": "2037-12-31T01:00:00Z", "not_before": "2015-05-25T12:00:00Z", "public_key_algorithm": "RSA", "serial_number": "144918191876577076464031512351042010504348870", "sha1_fingerprint": "06b25927c42a721631c1efd9431e648fa62e1e39", "signature_algorithm": "SHA256-RSA", "subject": "CN=Amazon Root CA 1,O=Amazon,C=US", "version": 3}, {"cert_pem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIEXjCCA0agAwIBAgITB3MSOAudZoijOx7Zv5zNpo4ODzANBgkqhkiG9w0BAQsF\nADA5MQswCQYDVQQGEwJVUzEPMA0GA1UEChMGQW1hem9uMRkwFwYDVQQDExBBbWF6\nb24gUm9vdCBDQSAxMB4XDTIyMDgyMzIyMjEyOFoXDTMwMDgyMzIyMjEyOFowPDEL\nMAkGA1UEBhMCVVMxDzANBgNVBAoTBkFtYXpvbjEcMBoGA1UEAxMTQW1hem9uIFJT\nQSAyMDQ4IE0wMTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAOtxLKnL\nH4gokjIwr4pXD3i3NyWVVYesZ1yX0yLI2qIUZ2t88Gfa4gMqs1YSXca1R/lnCKeT\nepWSGA+0+fkQNpp/L4C2T7oTTsddUx7g3ZYzByDTlrwS5HRQQqEFE3O1T5tEJP4t\nf+28IoXsNiEzl3UGzicYgtzj2cWCB41eJgEmJmcf2T8TzzK6a614ZPyq/w4CPAff\nnAV4coz96nW3AyiE2uhuB4zQUIXvgVSycW7sbWLvj5TDXunEpNCRwC4kkZjK7rol\njtT2cbb7W2s4Bkg3R42G3PLqBvt2N32e/0JOTViCk8/iccJ4sXqrS1uUN4iB5Nmv\nJK74csVl+0u0UecCAwEAAaOCAVowggFWMBIGA1UdEwEB/wQIMAYBAf8CAQAwDgYD\nVR0PAQH/BAQDAgGGMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjAdBgNV\nHQ4EFgQUgbgOY4qJEhjl+js7UJWf5uWQE4UwHwYDVR0jBBgwFoAUhBjMhTTsvAyU\nlC4IWZzHshBOCggwewYIKwYBBQUHAQEEbzBtMC8GCCsGAQUFBzABhiNodHRwOi8v\nb2NzcC5yb290Y2ExLmFtYXpvbnRydXN0LmNvbTA6BggrBgEFBQcwAoYuaHR0cDov\nL2NydC5yb290Y2ExLmFtYXpvbnRydXN0LmNvbS9yb290Y2ExLmNlcjA/BgNVHR8E\nODA2MDSgMqAwhi5odHRwOi8vY3JsLnJvb3RjYTEuYW1hem9udHJ1c3QuY29tL3Jv\nb3RjYTEuY3JsMBMGA1UdIAQMMAowCAYGZ4EMAQIBMA0GCSqGSIb3DQEBCwUAA4IB\nAQCtAN4CBSMuBjJitGuxlBbkEUDeK/pZwTXv4KqPK0G50fOHOQAd8j21p0cMBgbG\nkfMHVwLU7b0XwZCav0h1ogdPMN1KakK1DT0VwA/+hFvGPJnMV1Kx2G4S1ZaSk0uU\n5QfoiYIIano01J5k4T2HapKQmmOhS/iPtuo00wW+IMLeBuKMn3OLn005hcrOGTad\nhcmeyfhQP7Z+iKHvyoQGi1C0ClymHETx/chhQGDyYSWqB/THwnN15AwLQo0E5V9E\nSJlbe4mBlqeInUsNYugExNf+tOiybcrswBy8OFsd34XOW3rjSUtsuafd9AWySa3h\nxRRrwszrzX/WWGm6wyB+f7C4\n-----END CERTIFICATE-----\n", "is_ca": true, "issuer": "CN=Amazon Root CA 1,O=Amazon,C=US", "not_after": "2030-08-23T22:21:28Z", "not_before": "2022-08-23T22:21:28Z", "public_key_algorithm": "RSA", "serial_number": "166129328851546858514271303855646110030630415", "sha1_fingerprint": "2ad974a775f73cbdbbd8f5ac3a49255fa8fb1f8c", "signature_algorithm": "SHA256-RSA", "subject": "CN=Amazon RSA 2048 M01,O=Amazon,C=US", "version": 3}, {"cert_pem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----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\n5i8vgozMw8ERpY+UBm4ZOptvdmHhfbN1dgpz0YtGIgm9WjdAntMt1BU=\n-----END CERTIFICATE-----\n", "is_ca": false, "issuer": "CN=Amazon RSA 2048 M01,O=Amazon,C=US", "not_after": "2024-05-12T23:59:59Z", "not_before": "2023-04-14T00:00:00Z", "public_key_algorithm": "RSA", "serial_number": "10860785064230208285910076270784010976", "sha1_fingerprint": "64902016cef886b3670ddcaafc385107494c7efe", "signature_algorithm": "SHA256-RSA", "subject": "CN=oidc.eks.ap-northeast-1.amazonaws.com", "version": 3}], "content": null, "id": "876ec6096b3f2e9d22b7016d735a7689929468a2", "url": "https://oidc.eks.ap-northeast-1.amazonaws.com/id/B3DE07E3C89D23D8EC6AA8DFD0272007", "verify_chain": true}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_eip", "name": "nat", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"address": null, "allocation_id": "eipalloc-00472b53471acb3c3", "associate_with_private_ip": null, "association_id": "eipassoc-05ff4dba515aa15a3", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-00472b53471acb3c3", "instance": "", "network_border_group": "ap-northeast-1", "network_interface": "eni-04904c2848a2d7a7c", "private_dns": "ip-10-0-85-193.ap-northeast-1.compute.internal", "private_ip": "***********", "public_dns": "ec2-54-64-137-236.ap-northeast-1.compute.amazonaws.com", "public_ip": "*************", "public_ipv4_pool": "amazon", "tags": {"Name": "nat-elastic-ip"}, "tags_all": {"Name": "nat-elastic-ip"}, "timeouts": null, "vpc": true}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjoxODAwMDAwMDAwMDAsInJlYWQiOjkwMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMDB9fQ=="}]}, {"mode": "managed", "type": "aws_eks_cluster", "name": "cluster", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"access_config": [{"authentication_mode": "API_AND_CONFIG_MAP", "bootstrap_cluster_creator_admin_permissions": false}], "arn": "arn:aws:eks:ap-northeast-1:************:cluster/uniopen", "certificate_authority": [{"data": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJZkJPSFNGYlBDeUF3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TkRBeU1UVXhORE0yTkRoYUZ3MHpOREF5TVRJeE5EUXhORGhhTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUUN6NzFEa0plZCtJT2hDVm9XbWRaTTF1VVJhdTB4NkJMaCtpNUVvOGo2WStWcGpKT2pHZDRZcVF1ZFYKcHU4dXVHdDdxa1h5NVVESEJsUTl0dUNDOGpUeVI5VWVlMlNFUEtGOUp3Vkl6K2YwWFBPQjhKR2FkbzJsTWZRKwpXblV4NC9oazVBUnErRnE2R2lXWmEvYmJIcCttM3VMY3FJU2F2OEFYU3pEMHZMZjBGSXNuYldUa1BacndiNTBtCjVjbWRaZ214NlhrK054NnhtVVV4S1krR2I0WVZVYnpXbUFkSzJaQ1dlMEYxYUtybWdsVzZNV2h1T2NybUpCU2wKeFlHUUtIMXhiQ1ZGMHpWazFxQkswTUxUZzVLdDBNODNlclNmeTJDYlEwSFFnTnpSemN4bkNPOU1DeDhsR3UxNwozZWhlZFRVR3U0OTBjcFNlVzh6WUxLb1BPZUZCQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJRM2lDSVFpdjU4Nk1xSW1URFRxUHI4MytrQ1dEQVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQ3ByZFdicmRxZAp0NzhiaDZiWFd0dG0yZ2QyWDQwRUtycktJNHc2T3VOZ2JPaEhUbWxGakpjOHBKb05NdGhJQ2F4V0ZHVnRhQ2p0ClN1TGN3OWhiT2FLcVFmUHpaTzlmWmp1Wk1uM2RSSmNYTjQ2QnlEUmhhQ25YaFZnTUlsUThqcUJ1Z3J6WmFBMncKRVpNemNrZEkrVkJZQldRZ0MxTURqc2FKa1Flc1plQ1AzOHRqQWw0WEJMdGRBRXoyRDYwQjdXSWlVMVQ2RHRCYQo1cDZhZVBPVzZzejVlZ3h1T1VYMWJRV2hkbE5XM3NiNEN0MzhMUE1zWVdVdGxTTnhabWp1eTd5ZDl0L3RVQ1k3CkpEeWZuWlBvQWV3WTkzd2xOc01FL21XUjlTSUtPZHM1ck5TbEd4d2wraWRxck1mZ1BUMTVYYjJiYVZYalpLdjYKZ29Qb2JtWGpkWU1FCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}], "cluster_id": null, "created_at": "2024-02-15 14:37:01.278 +0000 UTC", "enabled_cluster_log_types": [], "encryption_config": [], "endpoint": "https://B3DE07E3C89D23D8EC6AA8DFD0272007.gr7.ap-northeast-1.eks.amazonaws.com", "id": "uniopen", "identity": [{"oidc": [{"issuer": "https://oidc.eks.ap-northeast-1.amazonaws.com/id/B3DE07E3C89D23D8EC6AA8DFD0272007"}]}], "kubernetes_network_config": [{"ip_family": "ipv4", "service_ipv4_cidr": "**********/16", "service_ipv6_cidr": ""}], "name": "uniopen", "outpost_config": [], "platform_version": "eks.9", "role_arn": "arn:aws:iam::************:role/eks-cluster-uniopen", "status": "ACTIVE", "tags": {}, "tags_all": {}, "timeouts": null, "version": "1.32", "vpc_config": [{"cluster_security_group_id": "sg-0cd9918593e78b46b", "endpoint_private_access": true, "endpoint_public_access": true, "public_access_cidrs": ["************/32", "**************/29", "*************/32", "************/29", "*************/32"], "security_group_ids": ["sg-065ff73beb8fd069c"], "subnet_ids": ["subnet-007a652180de503c6", "subnet-09106d152651ed307", "subnet-0cdae13b1f4b1e19b", "subnet-0de9a13ff206133e6"], "vpc_id": "vpc-0b34906e9113590e4"}]}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjkwMDAwMDAwMDAwMCwidXBkYXRlIjozNjAwMDAwMDAwMDAwfX0=", "dependencies": ["aws_iam_role.eks-cluster", "aws_iam_role_policy_attachment.amazon-eks-cluster-policy", "aws_subnet.private-ap-northeast-1a", "aws_subnet.private-ap-northeast-1c", "aws_subnet.public-ap-northeast-1a", "aws_subnet.public-ap-northeast-1c", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_eks_fargate_profile", "name": "kube-system", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:eks:ap-northeast-1:************:fargateprofile/uniopen/kube-system/80c6d69d-ec25-6a9d-01fa-78caedba1425", "cluster_name": "uniopen", "fargate_profile_name": "kube-system", "id": "uniopen:kube-system", "pod_execution_role_arn": "arn:aws:iam::************:role/eks-fargate-profile", "selector": [{"labels": {}, "namespace": "kube-system"}], "status": "ACTIVE", "subnet_ids": ["subnet-007a652180de503c6", "subnet-0de9a13ff206133e6"], "tags": {}, "tags_all": {}, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwfX0=", "dependencies": ["aws_eks_cluster.cluster", "aws_iam_role.eks-cluster", "aws_iam_role.eks-fargate-profile", "aws_iam_role_policy_attachment.amazon-eks-cluster-policy", "aws_subnet.private-ap-northeast-1a", "aws_subnet.private-ap-northeast-1c", "aws_subnet.public-ap-northeast-1a", "aws_subnet.public-ap-northeast-1c", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_eks_fargate_profile", "name": "staging-v2", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:eks:ap-northeast-1:************:fargateprofile/uniopen/staging-v2/0acb405b-e038-4e40-a92e-e6ffcc868e6a", "cluster_name": "uniopen", "fargate_profile_name": "staging-v2", "id": "uniopen:staging-v2", "pod_execution_role_arn": "arn:aws:iam::************:role/eks-fargate-profile", "selector": [{"labels": {"compute-type": "fargate"}, "namespace": "staging"}], "status": "ACTIVE", "subnet_ids": ["subnet-007a652180de503c6", "subnet-0de9a13ff206133e6"], "tags": null, "tags_all": {}, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwfX0=", "dependencies": ["aws_eks_cluster.cluster", "aws_iam_role.eks-cluster", "aws_iam_role.eks-fargate-profile", "aws_iam_role_policy_attachment.amazon-eks-cluster-policy", "aws_subnet.private-ap-northeast-1a", "aws_subnet.private-ap-northeast-1c", "aws_subnet.public-ap-northeast-1a", "aws_subnet.public-ap-northeast-1c", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_iam_openid_connect_provider", "name": "eks", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:oidc-provider/oidc.eks.ap-northeast-1.amazonaws.com/id/B3DE07E3C89D23D8EC6AA8DFD0272007", "client_id_list": ["sts.amazonaws.com"], "id": "arn:aws:iam::************:oidc-provider/oidc.eks.ap-northeast-1.amazonaws.com/id/B3DE07E3C89D23D8EC6AA8DFD0272007", "tags": {}, "tags_all": {}, "thumbprint_list": ["9e99a48a9960b14926bb7f3b02e22da2b0ab7280"], "url": "oidc.eks.ap-northeast-1.amazonaws.com/id/B3DE07E3C89D23D8EC6AA8DFD0272007"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eks_cluster.cluster", "aws_iam_role.eks-cluster", "aws_iam_role_policy_attachment.amazon-eks-cluster-policy", "aws_subnet.private-ap-northeast-1a", "aws_subnet.private-ap-northeast-1c", "aws_subnet.public-ap-northeast-1a", "aws_subnet.public-ap-northeast-1c", "aws_vpc.uniopen-vpc", "data.tls_certificate.eks"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "aws_load_balancer_controller", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/AWSLoadBalancerController", "description": "", "id": "arn:aws:iam::************:policy/AWSLoadBalancerController", "name": "AWSLoadBalancerController", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"iam:CreateServiceLinkedRole\"],\"Condition\":{\"StringEquals\":{\"iam:AWSServiceName\":\"elasticloadbalancing.amazonaws.com\"}},\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"ec2:DescribeAccountAttributes\",\"ec2:DescribeAddresses\",\"ec2:DescribeAvailabilityZones\",\"ec2:DescribeInternetGateways\",\"ec2:DescribeVpcs\",\"ec2:DescribeVpcPeeringConnections\",\"ec2:DescribeSubnets\",\"ec2:DescribeSecurityGroups\",\"ec2:DescribeInstances\",\"ec2:DescribeNetworkInterfaces\",\"ec2:DescribeTags\",\"ec2:GetCoipPoolUsage\",\"ec2:DescribeCoipPools\",\"elasticloadbalancing:DescribeLoadBalancers\",\"elasticloadbalancing:DescribeLoadBalancerAttributes\",\"elasticloadbalancing:DescribeListeners\",\"elasticloadbalancing:DescribeListenerCertificates\",\"elasticloadbalancing:DescribeSSLPolicies\",\"elasticloadbalancing:DescribeRules\",\"elasticloadbalancing:DescribeTargetGroups\",\"elasticloadbalancing:DescribeTargetGroupAttributes\",\"elasticloadbalancing:DescribeTargetHealth\",\"elasticloadbalancing:DescribeTags\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"cognito-idp:DescribeUserPoolClient\",\"acm:ListCertificates\",\"acm:DescribeCertificate\",\"iam:ListServerCertificates\",\"iam:GetServerCertificate\",\"waf-regional:GetWebACL\",\"waf-regional:GetWebACLForResource\",\"waf-regional:AssociateWebACL\",\"waf-regional:DisassociateWebACL\",\"wafv2:GetWebACL\",\"wafv2:GetWebACLForResource\",\"wafv2:AssociateWebACL\",\"wafv2:DisassociateWebACL\",\"shield:GetSubscriptionState\",\"shield:DescribeProtection\",\"shield:CreateProtection\",\"shield:DeleteProtection\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"ec2:AuthorizeSecurityGroupIngress\",\"ec2:RevokeSecurityGroupIngress\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"ec2:CreateSecurityGroup\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"ec2:CreateTags\"],\"Condition\":{\"Null\":{\"aws:RequestTag/elbv2.k8s.aws/cluster\":\"false\"},\"StringEquals\":{\"ec2:CreateAction\":\"CreateSecurityGroup\"}},\"Effect\":\"Allow\",\"Resource\":\"arn:aws:ec2:*:*:security-group/*\"},{\"Action\":[\"ec2:CreateTags\",\"ec2:DeleteTags\"],\"Condition\":{\"Null\":{\"aws:RequestTag/elbv2.k8s.aws/cluster\":\"true\",\"aws:ResourceTag/elbv2.k8s.aws/cluster\":\"false\"}},\"Effect\":\"Allow\",\"Resource\":\"arn:aws:ec2:*:*:security-group/*\"},{\"Action\":[\"ec2:AuthorizeSecurityGroupIngress\",\"ec2:RevokeSecurityGroupIngress\",\"ec2:DeleteSecurityGroup\"],\"Condition\":{\"Null\":{\"aws:ResourceTag/elbv2.k8s.aws/cluster\":\"false\"}},\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"elasticloadbalancing:CreateLoadBalancer\",\"elasticloadbalancing:CreateTargetGroup\"],\"Condition\":{\"Null\":{\"aws:RequestTag/elbv2.k8s.aws/cluster\":\"false\"}},\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"elasticloadbalancing:CreateListener\",\"elasticloadbalancing:DeleteListener\",\"elasticloadbalancing:CreateRule\",\"elasticloadbalancing:DeleteRule\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"elasticloadbalancing:AddTags\",\"elasticloadbalancing:RemoveTags\"],\"Condition\":{\"Null\":{\"aws:RequestTag/elbv2.k8s.aws/cluster\":\"true\",\"aws:ResourceTag/elbv2.k8s.aws/cluster\":\"false\"}},\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:elasticloadbalancing:*:*:targetgroup/*/*\",\"arn:aws:elasticloadbalancing:*:*:loadbalancer/net/*/*\",\"arn:aws:elasticloadbalancing:*:*:loadbalancer/app/*/*\"]},{\"Action\":[\"elasticloadbalancing:AddTags\",\"elasticloadbalancing:RemoveTags\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:elasticloadbalancing:*:*:listener/net/*/*/*\",\"arn:aws:elasticloadbalancing:*:*:listener/app/*/*/*\",\"arn:aws:elasticloadbalancing:*:*:listener-rule/net/*/*/*\",\"arn:aws:elasticloadbalancing:*:*:listener-rule/app/*/*/*\"]},{\"Action\":[\"elasticloadbalancing:ModifyLoadBalancerAttributes\",\"elasticloadbalancing:SetIpAddressType\",\"elasticloadbalancing:SetSecurityGroups\",\"elasticloadbalancing:SetSubnets\",\"elasticloadbalancing:DeleteLoadBalancer\",\"elasticloadbalancing:ModifyTargetGroup\",\"elasticloadbalancing:ModifyTargetGroupAttributes\",\"elasticloadbalancing:DeleteTargetGroup\"],\"Condition\":{\"Null\":{\"aws:ResourceTag/elbv2.k8s.aws/cluster\":\"false\"}},\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"elasticloadbalancing:AddTags\"],\"Condition\":{\"Null\":{\"aws:RequestTag/elbv2.k8s.aws/cluster\":\"false\"},\"StringEquals\":{\"elasticloadbalancing:CreateAction\":[\"CreateTargetGroup\",\"CreateLoadBalancer\"]}},\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:elasticloadbalancing:*:*:targetgroup/*/*\",\"arn:aws:elasticloadbalancing:*:*:loadbalancer/net/*/*\",\"arn:aws:elasticloadbalancing:*:*:loadbalancer/app/*/*\"]},{\"Action\":[\"elasticloadbalancing:RegisterTargets\",\"elasticloadbalancing:DeregisterTargets\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:elasticloadbalancing:*:*:targetgroup/*/*\"},{\"Action\":[\"elasticloadbalancing:SetWebAcl\",\"elasticloadbalancing:ModifyListener\",\"elasticloadbalancing:AddListenerCertificates\",\"elasticloadbalancing:RemoveListenerCertificates\",\"elasticloadbalancing:ModifyRule\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNIRIHZY7UU", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "aws_load_balancer_controller", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/aws-load-balancer-controller", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRoleWithWebIdentity\",\"Condition\":{\"StringEquals\":{\"oidc.eks.ap-northeast-1.amazonaws.com/id/B3DE07E3C89D23D8EC6AA8DFD0272007:sub\":\"system:serviceaccount:kube-system:aws-load-balancer-controller\"}},\"Effect\":\"Allow\",\"Principal\":{\"Federated\":\"arn:aws:iam::************:oidc-provider/oidc.eks.ap-northeast-1.amazonaws.com/id/B3DE07E3C89D23D8EC6AA8DFD0272007\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2024-03-03T10:59:39Z", "description": "", "force_detach_policies": false, "id": "aws-load-balancer-controller", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::************:policy/AWSLoadBalancerController"], "max_session_duration": 3600, "name": "aws-load-balancer-controller", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAW3MEEVGNE5H4ITMFS"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eks_cluster.cluster", "aws_iam_openid_connect_provider.eks", "aws_iam_role.eks-cluster", "aws_iam_role_policy_attachment.amazon-eks-cluster-policy", "aws_subnet.private-ap-northeast-1a", "aws_subnet.private-ap-northeast-1c", "aws_subnet.public-ap-northeast-1a", "aws_subnet.public-ap-northeast-1c", "aws_vpc.uniopen-vpc", "data.aws_iam_policy_document.aws_load_balancer_controller_assume_role_policy", "data.tls_certificate.eks"]}]}, {"mode": "managed", "type": "aws_iam_role", "name": "eks-cluster", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/eks-cluster-uniopen", "assume_role_policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"eks.amazonaws.com\"},\"Action\":\"sts:AssumeRole\"}]}", "create_date": "2024-02-15T14:36:43Z", "description": "", "force_detach_policies": false, "id": "eks-cluster-uniopen", "inline_policy": [{"name": "", "policy": ""}], "managed_policy_arns": ["arn:aws:iam::************:policy/eks-fargate-logging-policy", "arn:aws:iam::aws:policy/AWSXrayWriteOnlyAccess", "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy", "arn:aws:iam::aws:policy/service-role/AmazonEFSCSIDriverPolicy"], "max_session_duration": 3600, "name": "eks-cluster-uniopen", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAW3MEEVGNCO6U3VIHQ"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "eks-fargate-profile", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/eks-fargate-profile", "assume_role_policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"eks-fargate-pods.amazonaws.com\"},\"Action\":\"sts:AssumeRole\"}]}", "create_date": "2024-02-15T14:36:43Z", "description": "", "force_detach_policies": false, "id": "eks-fargate-profile", "inline_policy": [{"name": "", "policy": ""}], "managed_policy_arns": ["arn:aws:iam::************:policy/eks-fargate-logging-policy", "arn:aws:iam::aws:policy/AmazonEKSFargatePodExecutionRolePolicy"], "max_session_duration": 3600, "name": "eks-fargate-profile", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAW3MEEVGNKD2DJGJNH"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "amazon-eks-cluster-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "eks-cluster-uniopen-20240215143647694400000002", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy", "role": "eks-cluster-uniopen"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks-cluster"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "aws_load_balancer_controller_attach", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "aws-load-balancer-controller-20240303105940336300000001", "policy_arn": "arn:aws:iam::************:policy/AWSLoadBalancerController", "role": "aws-load-balancer-controller"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eks_cluster.cluster", "aws_iam_openid_connect_provider.eks", "aws_iam_policy.aws_load_balancer_controller", "aws_iam_role.aws_load_balancer_controller", "aws_iam_role.eks-cluster", "aws_iam_role_policy_attachment.amazon-eks-cluster-policy", "aws_subnet.private-ap-northeast-1a", "aws_subnet.private-ap-northeast-1c", "aws_subnet.public-ap-northeast-1a", "aws_subnet.public-ap-northeast-1c", "aws_vpc.uniopen-vpc", "data.aws_iam_policy_document.aws_load_balancer_controller_assume_role_policy", "data.tls_certificate.eks"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "eks-fargate-profile", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "eks-fargate-profile-20240215143647687400000001", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKSFargatePodExecutionRolePolicy", "role": "eks-fargate-profile"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks-fargate-profile"]}]}, {"mode": "managed", "type": "aws_internet_gateway", "name": "igw", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:internet-gateway/igw-0d3ac5f442b51e88c", "id": "igw-0d3ac5f442b51e88c", "owner_id": "************", "tags": {"Name": "uniopen-igw"}, "tags_all": {"Name": "uniopen-igw"}, "timeouts": null, "vpc_id": "vpc-0b34906e9113590e4"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_nat_gateway", "name": "nat-ap-northeast-1a", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"allocation_id": "eipalloc-00472b53471acb3c3", "association_id": "eipassoc-05ff4dba515aa15a3", "connectivity_type": "public", "id": "nat-0ae6177f17ad93fe8", "network_interface_id": "eni-04904c2848a2d7a7c", "private_ip": "***********", "public_ip": "*************", "secondary_allocation_ids": [], "secondary_private_ip_address_count": 0, "secondary_private_ip_addresses": [], "subnet_id": "subnet-09106d152651ed307", "tags": {"Name": "nat-ap-northeast-1a"}, "tags_all": {"Name": "nat-ap-northeast-1a"}, "timeouts": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.igw", "aws_subnet.public-ap-northeast-1a", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:route-table/rtb-00df32064d6caa6c0", "id": "rtb-00df32064d6caa6c0", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "nat-0ae6177f17ad93fe8", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Name": "private"}, "tags_all": {"Name": "private"}, "timeouts": null, "vpc_id": "vpc-0b34906e9113590e4"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.igw", "aws_nat_gateway.nat-ap-northeast-1a", "aws_subnet.public-ap-northeast-1a", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:route-table/rtb-0ec091ceb4e7be3c4", "id": "rtb-0ec091ceb4e7be3c4", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "igw-0d3ac5f442b51e88c", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Name": "public"}, "tags_all": {"Name": "public"}, "timeouts": null, "vpc_id": "vpc-0b34906e9113590e4"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.igw", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "private-ap-northeast-1a", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0b56daadb99c2e4a5", "route_table_id": "rtb-00df32064d6caa6c0", "subnet_id": "subnet-007a652180de503c6", "timeouts": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.igw", "aws_nat_gateway.nat-ap-northeast-1a", "aws_route_table.private", "aws_subnet.private-ap-northeast-1a", "aws_subnet.public-ap-northeast-1a", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "private-ap-northeast-1c", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-000658711c97a07d1", "route_table_id": "rtb-00df32064d6caa6c0", "subnet_id": "subnet-0de9a13ff206133e6", "timeouts": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.igw", "aws_nat_gateway.nat-ap-northeast-1a", "aws_route_table.private", "aws_subnet.private-ap-northeast-1c", "aws_subnet.public-ap-northeast-1a", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "public-ap-northeast-1a", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0a4893e26717c5666", "route_table_id": "rtb-0ec091ceb4e7be3c4", "subnet_id": "subnet-09106d152651ed307", "timeouts": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_internet_gateway.igw", "aws_route_table.public", "aws_subnet.public-ap-northeast-1a", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "public-ap-northeast-1c", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-07c4c67ab15217ab1", "route_table_id": "rtb-0ec091ceb4e7be3c4", "subnet_id": "subnet-0cdae13b1f4b1e19b", "timeouts": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_internet_gateway.igw", "aws_route_table.public", "aws_subnet.public-ap-northeast-1c", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_security_group", "name": "doc-db-sg", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:security-group/sg-0b759b9b09db7c8a8", "description": "Security group for my application", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0b759b9b09db7c8a8", "ingress": [{"cidr_blocks": ["10.0.0.0/16"], "description": "", "from_port": 22, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 22}, {"cidr_blocks": ["*************/32"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}, {"cidr_blocks": [], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": ["sg-0725859b22c418538"], "self": false, "to_port": 0}, {"cidr_blocks": [], "description": "", "from_port": 14001, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0cd9918593e78b46b"], "self": false, "to_port": 14003}, {"cidr_blocks": [], "description": "", "from_port": 14002, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0cd9918593e78b46b"], "self": false, "to_port": 14002}, {"cidr_blocks": [], "description": "", "from_port": 27017, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0725859b22c418538"], "self": false, "to_port": 27017}, {"cidr_blocks": [], "description": "", "from_port": 27017, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0cd9918593e78b46b"], "self": false, "to_port": 27017}, {"cidr_blocks": [], "description": "cloud9", "from_port": 27017, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-09e4747bcd22c1d1d"], "self": false, "to_port": 27017}, {"cidr_blocks": [], "description": "jumper", "from_port": 27017, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-025712c593365850e"], "self": false, "to_port": 27017}, {"cidr_blocks": [], "description": "kafka", "from_port": 9098, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-065ff73beb8fd069c"], "self": false, "to_port": 9098}, {"cidr_blocks": [], "description": "kafka", "from_port": 9098, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0cd9918593e78b46b"], "self": false, "to_port": 9098}], "name": "doc-db-sg", "name_prefix": "", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Name": "doc-db-sg"}, "tags_all": {"Name": "doc-db-sg"}, "timeouts": null, "vpc_id": "vpc-0b34906e9113590e4"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_security_group_rule", "name": "all_traffic_0725859b", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"cidr_blocks": null, "description": null, "from_port": 0, "id": "sgrule-697622405", "ipv6_cidr_blocks": null, "prefix_list_ids": null, "protocol": "-1", "security_group_id": "sg-0b759b9b09db7c8a8", "security_group_rule_id": "sgr-078cfcc31d3e61eef", "self": false, "source_security_group_id": "sg-0725859b22c418538", "timeouts": null, "to_port": 0, "type": "ingress"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_security_group.doc-db-sg", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_security_group_rule", "name": "all_traffic_ip", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"cidr_blocks": ["*************/32"], "description": null, "from_port": 0, "id": "sgrule-**********", "ipv6_cidr_blocks": null, "prefix_list_ids": null, "protocol": "-1", "security_group_id": "sg-0b759b9b09db7c8a8", "security_group_rule_id": "sgr-0cb80da9486b8b1d3", "self": false, "source_security_group_id": null, "timeouts": null, "to_port": 0, "type": "ingress"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_security_group.doc-db-sg", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_security_group_rule", "name": "cloud9_tcp_27017", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"cidr_blocks": null, "description": "cloud9", "from_port": 27017, "id": "sgrule-**********", "ipv6_cidr_blocks": null, "prefix_list_ids": null, "protocol": "tcp", "security_group_id": "sg-0b759b9b09db7c8a8", "security_group_rule_id": "sgr-06215f09435a659bf", "self": false, "source_security_group_id": "sg-09e4747bcd22c1d1d", "timeouts": null, "to_port": 27017, "type": "ingress"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_security_group.doc-db-sg", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_security_group_rule", "name": "ssh_10_0_0_0_16", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"cidr_blocks": ["10.0.0.0/16"], "description": null, "from_port": 22, "id": "sgrule-**********", "ipv6_cidr_blocks": null, "prefix_list_ids": null, "protocol": "tcp", "security_group_id": "sg-0b759b9b09db7c8a8", "security_group_rule_id": "sgr-03576c48addbbf860", "self": false, "source_security_group_id": null, "timeouts": null, "to_port": 22, "type": "ingress"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_security_group.doc-db-sg", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_security_group_rule", "name": "tcp_14001_ocd99", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"cidr_blocks": null, "description": null, "from_port": 14001, "id": "sgrule-**********", "ipv6_cidr_blocks": null, "prefix_list_ids": null, "protocol": "tcp", "security_group_id": "sg-0b759b9b09db7c8a8", "security_group_rule_id": "sgr-034d684eed1e3a6da", "self": false, "source_security_group_id": "sg-0cd9918593e78b46b", "timeouts": null, "to_port": 14001, "type": "ingress"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_security_group.doc-db-sg", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_security_group_rule", "name": "tcp_14002_ocd99", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"cidr_blocks": null, "description": null, "from_port": 14002, "id": "sgrule-123911569", "ipv6_cidr_blocks": null, "prefix_list_ids": null, "protocol": "tcp", "security_group_id": "sg-0b759b9b09db7c8a8", "security_group_rule_id": "sgr-0a254d71f63b74bd9", "self": false, "source_security_group_id": "sg-0cd9918593e78b46b", "timeouts": null, "to_port": 14002, "type": "ingress"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_security_group.doc-db-sg", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_security_group_rule", "name": "tcp_27017_0725859b", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"cidr_blocks": null, "description": null, "from_port": 27017, "id": "sgrule-**********", "ipv6_cidr_blocks": null, "prefix_list_ids": null, "protocol": "tcp", "security_group_id": "sg-0b759b9b09db7c8a8", "security_group_rule_id": "sgr-00cd4cf326f7a37cc", "self": false, "source_security_group_id": "sg-0725859b22c418538", "timeouts": null, "to_port": 27017, "type": "ingress"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_security_group.doc-db-sg", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_security_group_rule", "name": "tcp_27017_felix", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"cidr_blocks": null, "description": "felix-ssh", "from_port": 27017, "id": "sgrule-**********", "ipv6_cidr_blocks": null, "prefix_list_ids": null, "protocol": "tcp", "security_group_id": "sg-0b759b9b09db7c8a8", "security_group_rule_id": "sgr-0dcf3057de14e85fa", "self": false, "source_security_group_id": "sg-065ff73beb8fd069c", "timeouts": null, "to_port": 27017, "type": "ingress"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_security_group.doc-db-sg", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_security_group_rule", "name": "tcp_27017_jumper", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"cidr_blocks": null, "description": "jumper", "from_port": 27017, "id": "sgrule-**********", "ipv6_cidr_blocks": null, "prefix_list_ids": null, "protocol": "tcp", "security_group_id": "sg-0b759b9b09db7c8a8", "security_group_rule_id": "sgr-0e366f31346a61350", "self": false, "source_security_group_id": "sg-025712c593365850e", "timeouts": null, "to_port": 27017, "type": "ingress"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_security_group.doc-db-sg", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_security_group_rule", "name": "tcp_27017_ocd99", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"cidr_blocks": null, "description": null, "from_port": 27017, "id": "sgrule-**********", "ipv6_cidr_blocks": null, "prefix_list_ids": null, "protocol": "tcp", "security_group_id": "sg-0b759b9b09db7c8a8", "security_group_rule_id": "sgr-0da36fb6eb12bcc4f", "self": false, "source_security_group_id": "sg-0cd9918593e78b46b", "timeouts": null, "to_port": 27017, "type": "ingress"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_security_group.doc-db-sg", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_security_group_rule", "name": "tcp_9098_kafka", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"cidr_blocks": null, "description": "kafka", "from_port": 9098, "id": "sgrule-**********", "ipv6_cidr_blocks": null, "prefix_list_ids": null, "protocol": "tcp", "security_group_id": "sg-0b759b9b09db7c8a8", "security_group_rule_id": "sgr-04e0a37209da86eca", "self": false, "source_security_group_id": "sg-065ff73beb8fd069c", "timeouts": null, "to_port": 9098, "type": "ingress"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_security_group.doc-db-sg", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_security_group_rule", "name": "tcp_9098_kafka_2", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"cidr_blocks": null, "description": "kafka", "from_port": 9098, "id": "sgrule-**********", "ipv6_cidr_blocks": null, "prefix_list_ids": null, "protocol": "tcp", "security_group_id": "sg-0b759b9b09db7c8a8", "security_group_rule_id": "sgr-0029c5efcaf5ee74f", "self": false, "source_security_group_id": "sg-0cd9918593e78b46b", "timeouts": null, "to_port": 9098, "type": "ingress"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_security_group.doc-db-sg", "aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "private-ap-northeast-1a", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:subnet/subnet-007a652180de503c6", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-northeast-1a", "availability_zone_id": "apne1-az4", "cidr_block": "10.0.0.0/19", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-007a652180de503c6", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "private-ap-northeast-1a", "kubernetes.io/cluster/uniopen": "owned", "kubernetes.io/role/internal-elb": "1"}, "tags_all": {"Name": "private-ap-northeast-1a", "kubernetes.io/cluster/uniopen": "owned", "kubernetes.io/role/internal-elb": "1"}, "timeouts": null, "vpc_id": "vpc-0b34906e9113590e4"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "private-ap-northeast-1c", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:subnet/subnet-0de9a13ff206133e6", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-northeast-1c", "availability_zone_id": "apne1-az1", "cidr_block": "*********/19", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0de9a13ff206133e6", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "private-ap-northeast-1c", "kubernetes.io/cluster/uniopen": "owned", "kubernetes.io/role/internal-elb": "1"}, "tags_all": {"Name": "private-ap-northeast-1c", "kubernetes.io/cluster/uniopen": "owned", "kubernetes.io/role/internal-elb": "1"}, "timeouts": null, "vpc_id": "vpc-0b34906e9113590e4"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "public-ap-northeast-1a", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:subnet/subnet-09106d152651ed307", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-northeast-1a", "availability_zone_id": "apne1-az4", "cidr_block": "*********/19", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-09106d152651ed307", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "public-ap-northeast-1a", "kubernetes.io/cluster/uniopen": "owned", "kubernetes.io/role/elb": "1"}, "tags_all": {"Name": "public-ap-northeast-1a", "kubernetes.io/cluster/uniopen": "owned", "kubernetes.io/role/elb": "1"}, "timeouts": null, "vpc_id": "vpc-0b34906e9113590e4"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "public-ap-northeast-1c", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:subnet/subnet-0cdae13b1f4b1e19b", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-northeast-1c", "availability_zone_id": "apne1-az1", "cidr_block": "*********/19", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0cdae13b1f4b1e19b", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "public-ap-northeast-1c", "kubernetes.io/cluster/uniopen": "owned", "kubernetes.io/role/elb": "1"}, "tags_all": {"Name": "public-ap-northeast-1c", "kubernetes.io/cluster/uniopen": "owned", "kubernetes.io/role/elb": "1"}, "timeouts": null, "vpc_id": "vpc-0b34906e9113590e4"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.uniopen-vpc"]}]}, {"mode": "managed", "type": "aws_vpc", "name": "uniopen-vpc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:vpc/vpc-0b34906e9113590e4", "assign_generated_ipv6_cidr_block": false, "cidr_block": "10.0.0.0/16", "default_network_acl_id": "acl-0a4c15f0f08b9be81", "default_route_table_id": "rtb-09c79067327a96ced", "default_security_group_id": "sg-0725859b22c418538", "dhcp_options_id": "dopt-002fa95043cb9cde6", "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-0b34906e9113590e4", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-09c79067327a96ced", "owner_id": "************", "tags": {"Name": "uniopen-vpc"}, "tags_all": {"Name": "uniopen-vpc"}}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}, {"mode": "managed", "type": "helm_release", "name": "aws-load-balancer-controller", "provider": "provider[\"registry.terraform.io/hashicorp/helm\"]", "instances": [{"schema_version": 1, "attributes": {"atomic": false, "chart": "aws-load-balancer-controller", "cleanup_on_fail": false, "create_namespace": false, "dependency_update": false, "description": null, "devel": null, "disable_crd_hooks": false, "disable_openapi_validation": false, "disable_webhooks": false, "force_update": false, "id": "aws-load-balancer-controller", "keyring": null, "lint": false, "manifest": null, "max_history": 0, "metadata": [{"app_version": "v2.4.1", "chart": "aws-load-balancer-controller", "name": "aws-load-balancer-controller", "namespace": "kube-system", "revision": 3, "values": "{\"clusterName\":\"uniopen\",\"image\":{\"tag\":\"v2.4.7\"},\"region\":\"ap-northeast-1\",\"replicaCount\":1,\"serviceAccount\":{\"annotations\":{\"eks.amazonaws.com/role-arn\":\"arn:aws:iam::************:role/aws-load-balancer-controller\"},\"name\":\"aws-load-balancer-controller\"},\"vpcId\":\"vpc-0b34906e9113590e4\"}", "version": "1.4.1"}], "name": "aws-load-balancer-controller", "namespace": "kube-system", "pass_credentials": false, "postrender": [], "recreate_pods": false, "render_subchart_notes": true, "replace": false, "repository": "https://aws.github.io/eks-charts", "repository_ca_file": null, "repository_cert_file": null, "repository_key_file": null, "repository_password": null, "repository_username": null, "reset_values": false, "reuse_values": false, "set": [{"name": "clusterName", "type": "", "value": "uniopen"}, {"name": "image.tag", "type": "", "value": "v2.4.7"}, {"name": "region", "type": "", "value": "ap-northeast-1"}, {"name": "replicaCount", "type": "", "value": "1"}, {"name": "serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn", "type": "", "value": "arn:aws:iam::************:role/aws-load-balancer-controller"}, {"name": "serviceAccount.name", "type": "", "value": "aws-load-balancer-controller"}, {"name": "vpcId", "type": "", "value": "vpc-0b34906e9113590e4"}], "set_list": [], "set_sensitive": [], "skip_crds": false, "status": "deployed", "timeout": 300, "values": null, "verify": false, "version": "1.4.1", "wait": true, "wait_for_jobs": false}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_eks_cluster.cluster", "aws_eks_fargate_profile.kube-system", "aws_iam_openid_connect_provider.eks", "aws_iam_role.aws_load_balancer_controller", "aws_iam_role.eks-cluster", "aws_iam_role.eks-fargate-profile", "aws_iam_role_policy_attachment.amazon-eks-cluster-policy", "aws_subnet.private-ap-northeast-1a", "aws_subnet.private-ap-northeast-1c", "aws_subnet.public-ap-northeast-1a", "aws_subnet.public-ap-northeast-1c", "aws_vpc.uniopen-vpc", "data.aws_iam_policy_document.aws_load_balancer_controller_assume_role_policy", "data.tls_certificate.eks"]}]}, {"mode": "managed", "type": "helm_release", "name": "metrics-server", "provider": "provider[\"registry.terraform.io/hashicorp/helm\"]", "instances": [{"schema_version": 1, "attributes": {"atomic": false, "chart": "metrics-server", "cleanup_on_fail": false, "create_namespace": false, "dependency_update": false, "description": null, "devel": null, "disable_crd_hooks": false, "disable_openapi_validation": false, "disable_webhooks": false, "force_update": false, "id": "metrics-server", "keyring": null, "lint": false, "manifest": null, "max_history": 0, "metadata": [{"app_version": "0.6.1", "chart": "metrics-server", "name": "metrics-server", "namespace": "kube-system", "revision": 1, "values": "{\"metrics\":{\"enabled\":false}}", "version": "3.8.2"}], "name": "metrics-server", "namespace": "kube-system", "pass_credentials": false, "postrender": [], "recreate_pods": false, "render_subchart_notes": true, "replace": false, "repository": "https://kubernetes-sigs.github.io/metrics-server/", "repository_ca_file": null, "repository_cert_file": null, "repository_key_file": null, "repository_password": null, "repository_username": null, "reset_values": false, "reuse_values": false, "set": [{"name": "metrics.enabled", "type": "", "value": "false"}, {"name": "nodeSelector.nodegroup", "type": "", "value": "services"}], "set_list": [], "set_sensitive": [], "skip_crds": false, "status": "deployed", "timeout": 300, "values": null, "verify": false, "version": "3.8.2", "wait": true, "wait_for_jobs": false}, "sensitive_attributes": [[{"type": "get_attr", "value": "repository_password"}]], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_eks_cluster.cluster", "aws_eks_fargate_profile.kube-system", "aws_iam_role.eks-cluster", "aws_iam_role.eks-fargate-profile", "aws_iam_role_policy_attachment.amazon-eks-cluster-policy", "aws_subnet.private-ap-northeast-1a", "aws_subnet.private-ap-northeast-1c", "aws_subnet.public-ap-northeast-1a", "aws_subnet.public-ap-northeast-1c", "aws_vpc.uniopen-vpc"]}]}], "check_results": null}