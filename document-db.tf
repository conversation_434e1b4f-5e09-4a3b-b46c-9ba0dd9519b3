
resource "aws_docdb_subnet_group" "documentdb_subnet" {
  name       = "documentdb_subnet"
  subnet_ids = [aws_subnet.private-ap-northeast-1a.id, aws_subnet.private-ap-northeast-1c.id]
}
#
#data "aws_subnet_ids" "all" {
#  vpc_id = aws_vpc.uniopen-vpc.id
#}

resource "aws_docdb_cluster_parameter_group" "documentdb_cluster_parameter_group" {
  family = "docdb5.0"
  name   = "documentdb-cluster-parameter-group"

  parameter {
    name  = "tls"
    value = "enabled"
  }
}

resource "aws_docdb_cluster" "uniopen-document-cluster" {
  cluster_identifier      = "uniopen-document-cluster"
  engine_version          = "5.0.0"
  availability_zones       = ["ap-northeast-1a", "ap-northeast-1c"]
  db_cluster_parameter_group_name = aws_docdb_cluster_parameter_group.documentdb_cluster_parameter_group.name
  #instance_type           = "db.r5.2xlarge"
  master_username         = "uniopen"
  master_password         = "your_secure_password"
  vpc_security_group_ids  = ["sg-0725859b22c418538", aws_security_group.doc-db-sg.id] # Replace with your security group IDs

  db_subnet_group_name    = aws_docdb_subnet_group.documentdb_subnet.name
}

#resource "aws_docdb_instance" "uniopen-" {
#  identifier            = "example-instance"
#  instance_class        = "db.r5.large"
#  cluster_identifier    = aws_docdb_cluster.uniopen-document-cluster.id
#}

resource "aws_docdb_cluster_instance" "cluster_instances" {
  count              = 2
  identifier         = "docdb-cluster-demo-${count.index}"
  cluster_identifier = aws_docdb_cluster.uniopen-document-cluster.id
  instance_class     = "db.r5.large"
}

output "documentdb_cluster_endpoint" {
  value = aws_docdb_cluster.uniopen-document-cluster.endpoint
}
