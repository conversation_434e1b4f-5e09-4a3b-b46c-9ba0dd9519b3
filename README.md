# add service account

## token server
```shell
# cluster: uniopen
# service account: token-service-account
# iam role name: token-assume-role
# policy token_server_dynamodb_policy
eksctl create iamserviceaccount --name token-service-account --namespace staging --cluster uniopen --role-name token-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/token_server_dynamodb_policy --region ap-northeast-1 --approve
```

## www
```
eksctl create iamserviceaccount --name www-account --namespace staging --cluster uniopen --role-name www-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/www-service-account-policy --region ap-northeast-1 --approve --profile staging --override-existing-serviceaccounts 
```

## member
```
eksctl create iamserviceaccount --name member-account --namespace staging --cluster uniopen --role-name member-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/member_dynamodb_policy --attach-policy-arn arn:aws:iam::************:policy/op_member_keys_policy --region ap-northeast-1 --approve
```

## weather
```
eksctl create iamserviceaccount --name weather-account --namespace staging --cluster uniopen --role-name weather-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/weather_auth_code_policy --region ap-northeast-1 --approve
```

## news
```
eksctl create iamserviceaccount --name news-account --namespace staging --cluster uniopen --role-name news-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/staging_news_sha_key --region ap-northeast-1 --approve
```

## media
```
eksctl create iamserviceaccount --name media-account --namespace staging --cluster uniopen --role-name media-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/media_keys_policy --region ap-northeast-1 --approve
```

## opinion
```
eksctl create iamserviceaccount --name opinion-account --namespace staging --cluster uniopen --role-name opinion-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/opinion-s3-policy --region ap-northeast-1 --approve
```

## ecsearch
```
eksctl create iamserviceaccount --name ecsearch-account --namespace staging --cluster uniopen --role-name ecsearch-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/opinion-s3-policy --region ap-northeast-1 --approve
```

## ecfeeder
```
eksctl create iamserviceaccount --name ecfeeder-account --namespace staging --cluster uniopen --role-name ecfeeder-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/ecsearch_keys_policy --region ap-northeast-1 --approve
```

## redirect
```
eksctl create iamserviceaccount --name redirect-account --namespace staging --cluster uniopen --role-name redirect-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/ecsearch_keys_policy --region ap-northeast-1 --approve
```

## location
```
eksctl create iamserviceaccount --name location-account --namespace staging --cluster uniopen --role-name location-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/ecsearch_keys_policy --region ap-northeast-1 --approve
```

## picasso
```
eksctl create iamserviceaccount --name picasso-account --namespace staging --cluster uniopen --role-name picasso-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/picasso-s3-policy --region ap-northeast-1 --approve
```

## authz
```
eksctl create iamserviceaccount --name authz-account --namespace staging --cluster uniopen --role-name authz-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/authz-iam-policy --region ap-northeast-1 --approve
```

## admin-api
```
eksctl create iamserviceaccount --name admin-api-account --namespace staging --cluster uniopen --role-name admin-api-assume-role \
     --attach-policy-arn arn:aws:iam::************:policy/admin-api-s3-policy \
     --attach-policy-arn arn:aws:iam::************:policy/admin-api_keys_policy \
     --attach-policy-arn arn:aws:iam::************:policy/admin_api_sqs_policy \
     --region ap-northeast-1 --approve
```

## coin
```
eksctl create iamserviceaccount --name coin-account --namespace staging --cluster uniopen --role-name coin-assume-role --override-existing-serviceaccounts \
    --attach-policy-arn arn:aws:iam::************:policy/coin_policy --region ap-northeast-1 --approve
```

## story
```
eksctl create iamserviceaccount --name story-account --namespace staging --cluster uniopen --role-name story-assume-role --override-existing-serviceaccounts \
    --attach-policy-arn arn:aws:iam::************:policy/story_policy --region ap-northeast-1 --approve
```


## poi
```
eksctl create iamserviceaccount --name poi-account --namespace staging --cluster uniopen --role-name poi-assume-role --override-existing-serviceaccounts \
    --attach-policy-arn arn:aws:iam::************:policy/poi_policy --region ap-northeast-1 --approve
```

## user
```
eksctl create iamserviceaccount --name user-account --namespace staging --cluster uniopen --role-name user-assume-role --override-existing-serviceaccounts \
    --attach-policy-arn arn:aws:iam::************:policy/user_policy --region ap-northeast-1 --approve
```

## strapi
```s
eksctl create iamserviceaccount --name strapi-account --namespace staging --cluster uniopen --role-name strapi-assume-role --override-existing-serviceaccounts \
    --attach-policy-arn arn:aws:iam::************:policy/strapi-policy --region ap-northeast-1 --approve
```
## admin-coupon
```
eksctl create iamserviceaccount --name admin-coupon-account --namespace staging --cluster uniopen --role-name admin-coupon-assume-role --override-existing-serviceaccounts \
    --attach-policy-arn arn:aws:iam::************:policy/admin-coupon_policy --region ap-northeast-1 --approve
```

## coupon
```
eksctl create iamserviceaccount --name coupon-account --namespace staging --cluster uniopen --role-name coupon-assume-role --override-existing-serviceaccounts \
    --attach-policy-arn arn:aws:iam::************:policy/coupon_policy --region ap-northeast-1 --approve
```

## reservation
```
eksctl create iamserviceaccount --name reservation-account --namespace staging --cluster uniopen --role-name reservation-assume-role --override-existing-serviceaccounts \
    --attach-policy-arn arn:aws:iam::************:policy/staging-reservation-policy --region ap-northeast-1 --approve
```