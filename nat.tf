resource "aws_eip" "nat" {
  domain = "vpc"

  tags = {
    Name = "nat-elastic-ip"
  }
}

resource "aws_nat_gateway" "nat-ap-northeast-1a" {
  allocation_id = aws_eip.nat.id
  subnet_id     = aws_subnet.public-ap-northeast-1a.id

  tags = {
    Name = "nat-ap-northeast-1a"
  }

  depends_on = [aws_internet_gateway.igw]
}

# TBD
#resource "aws_nat_gateway" "nat-ap-northeast-1c" {
#  allocation_id = aws_eip.nat.id
#  subnet_id     = aws_subnet.public-ap-northeast-1c.id
#
#  tags = {
#    Name = "nat-ap-northeast-1c"
#  }
#
#  depends_on = [aws_internet_gateway.igw]
#}