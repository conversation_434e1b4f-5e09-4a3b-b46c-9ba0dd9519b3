provider "aws" {
  region = "ap-northeast-1"
  profile = "staging"
}

data "aws_iam_policy_document" "www-service-account-policy" {
  statement {
    effect = "Allow"
    actions = [
      "secretsmanager:BatchGetSecretValue"
    ]
    resources = [
      "*"
    ]
  }
  statement {
    effect = "Allow"
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-platform-id-XmFCE5",
      "arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-pwd-poRV16",
      "arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-iv-poRV16",
      "arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-key-GytalI"
    ]
  }
  statement {
    actions = [
      "sqs:SendMessage",
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:GetQueueAttributes"
    ]
    resources = [
      "arn:aws:sqs:ap-northeast-1:************:*"
    ]
  }
  statement {
    effect = "Allow"
    actions = [
      "appconfig:StartConfigurationSession",
      "appconfig:GetLatestConfiguration"
    ]
    resources = [
      "arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb/environment/ef1fq2l/configuration/umdqbnq"
    ]
  }
}

resource "aws_iam_policy" "www-service-account-policy" {
  name        = "www-service-account-policy"
  description = "www access secret manager for op news"
  policy      = data.aws_iam_policy_document.www-service-account-policy.json
}