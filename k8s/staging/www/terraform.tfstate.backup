{"version": 4, "terraform_version": "1.5.7", "serial": 15, "lineage": "34d4e9e5-da0d-8745-9845-8d2b5a991d78", "outputs": {}, "resources": [{"mode": "data", "type": "aws_iam_policy_document", "name": "www-service-account-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"secretsmanager:BatchGetSecretValue\",\n      \"Resource\": \"*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": [\n        \"arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-pwd-poRV16\",\n        \"arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-platform-id-XmFCE5\",\n        \"arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-key-GytalI\",\n        \"arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-iv-poRV16\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"sqs:SendMessage\",\n        \"sqs:ReceiveMessage\",\n        \"sqs:GetQueueAttributes\",\n        \"sqs:DeleteMessage\"\n      ],\n      \"Resource\": \"arn:aws:sqs:ap-northeast-1:************:*\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":\"secretsmanager:BatchGetSecretValue\",\"Resource\":\"*\"},{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-pwd-poRV16\",\"arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-platform-id-XmFCE5\",\"arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-key-GytalI\",\"arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-iv-poRV16\"]},{\"Effect\":\"Allow\",\"Action\":[\"sqs:SendMessage\",\"sqs:ReceiveMessage\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\"],\"Resource\":\"arn:aws:sqs:ap-northeast-1:************:*\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:BatchGetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}, {"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-iv-poRV16", "arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-key-GytalI", "arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-platform-id-XmFCE5", "arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-pwd-poRV16"], "sid": ""}, {"actions": ["sqs:DeleteMessage", "sqs:Get<PERSON>ueueAttributes", "sqs:ReceiveMessage", "sqs:SendMessage"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:sqs:ap-northeast-1:************:*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "www-service-account-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/www-service-account-policy", "attachment_count": 1, "description": "www access secret manager for op news", "id": "arn:aws:iam::************:policy/www-service-account-policy", "name": "www-service-account-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":\"secretsmanager:BatchGetSecretValue\",\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-pwd-poRV16\",\"arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-platform-id-XmFCE5\",\"arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-key-GytalI\",\"arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-opnews-iv-poRV16\"]},{\"Action\":[\"sqs:SendMessage\",\"sqs:ReceiveMessage\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:sqs:ap-northeast-1:************:*\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNMIAW6GVXS", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.www-service-account-policy"]}]}], "check_results": null}