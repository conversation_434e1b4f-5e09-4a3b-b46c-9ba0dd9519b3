---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: www
  namespace: staging
spec:
  minReadySeconds: 15
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      run: www
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: www
    spec:
      serviceAccountName: www-account
      nodeSelector:
        nodegroup: "services"
      tolerations:
        - key: "servicesOnly"
          operator: "Equal"
          value: "true"
          effect: "NoSchedule"
      containers:
        - name: www
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/www:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          resources:
            limits:
              cpu: 250m
              memory: 256Mi
            requests:
              cpu: 250m
              memory: 256Mi
