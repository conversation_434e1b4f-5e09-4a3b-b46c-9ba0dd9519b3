---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: token
  namespace: staging
spec:
  selector:
    matchLabels:
      run: token
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      annotations:
        #instrumentation.opentelemetry.io/inject-java: "true"
        prometheus.io/scrape: 'true'
        prometheus.io/port: '3000'
        prometheus.io/path: '/metrics'
      labels:
        run: token
        compute-type: fargate
    spec:
      serviceAccountName: token-service-account
      containers:
        - name: token
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/token:latest
          env:
            - name: ENV
              value: "staging"
          ports:
            - containerPort: 8000
          resources:
            limits:
              cpu: "1"
              memory: 1024Mi
            requests:
              cpu: "600m"
              memory: 256Mi

          startupProbe:
            httpGet:
              path: /hello/token
              port: 8000
              scheme: HTTP
            failureThreshold: 30
            periodSeconds: 10

          livenessProbe:
            httpGet:
              path: /hello/token
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 30
            successThreshold: 1
            failureThreshold: 3

          readinessProbe:
            httpGet:
              path: /hello/token
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 1
            timeoutSeconds: 30
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3