---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: https-endpoint
  namespace: staging
  annotations:
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:ap-northeast-1:471112919450:certificate/c8f20277-c53b-45ee-9d74-f2e4dd9d8b85"
    alb.ingress.kubernetes.io/wafv2-acl-arn: "arn:aws:wafv2:ap-northeast-1:471112919450:regional/webacl/uniopen-staging/bfdda1f9-06d7-4225-a0ab-65a14e949c8b"
    alb.ingress.kubernetes.io/healthcheck-path: "/healthcheck"
spec:
  ingressClassName: alb
  rules:
    - host: www.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: www
                port:
                  number: 3000
    - host: admin.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: admin
                port:
                  number: 80
    - host: token.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: token
                port:
                  number: 8000
    - host: member.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: member
                port:
                  number: 8000
    - host: weather.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: weather
                port:
                  number: 8000
    - host: news.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: news
                port:
                  number: 8000
    - host: media.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: media
                port:
                  number: 8000
    - host: opinion.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: opinion
                port:
                  number: 8000

    - host: admin-api.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: admin-api
                port:
                  number: 8000
    - host: strapi.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: strapi
                port:
                  number: 1337
    - host: ec.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: ecsearch
                port:
                  number: 8000
          - pathType: Prefix
            path: "/api/redirect/v1"
            backend:
              service:
                name: redirect
                port:
                  number: 8000
          - pathType: Prefix
            path: "/api/location/v1"
            backend:
              service:
                name: location
                port:
                  number: 8000
          - pathType: Prefix
            path: "/api/picasso"
            backend:
              service:
                name: picasso
                port:
                  number: 8080

    - host: pointcenter.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: point-center
                port:
                  number: 8080

    - host: marketing-campaign.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: marketing-campaign
                port:
                  number: 8000

    - host: coin.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: coin
                port:
                  number: 8000
    - host: admin-coupon.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: admin-coupon
                port:
                  number: 8000
    - host: coupon.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: coupon
                port:
                  number: 8000
    - host: social-api.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/story"
            backend:
              service:
                name: story
                port:
                  number: 8000
          - pathType: Prefix
            path: "/poi"
            backend:
              service:
                name: poi
                port:
                  number: 8000
          - pathType: Prefix
            path: "/user"
            backend:
              service:
                name: user
                port:
                  number: 8000
    - host: reservation.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: reservation
                port:
                  number: 8000
    - host: mbox.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: mbox
                port:
                  number: 8000
    - host: ci.dev.uniopen.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: woodpecker-server
                port:
                  number: 80
    - host: admin-map.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: admin-map
                port:
                  number: 8000
    - host: turborepo-cache.dev.uniopen.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: turborepo-remote-cache-server
                port:
                  number: 3000              



                  
