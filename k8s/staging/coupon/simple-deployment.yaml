---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: coupon
  namespace: staging
spec:
  selector:
    matchLabels:
      run: coupon
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: coupon
    spec:
      nodeSelector:
        nodegroup: "services"
      tolerations:
        - key: "servicesOnly"
          operator: "Equal"
          value: "true"
          effect: "NoSchedule"
      serviceAccountName: coupon-account
      containers:
        - name: coupon
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/coupon:latest
          imagePullPolicy: Always
          env:
            - name: CONFIG_PATH
              value: /app/config/staging.yaml
            - name: ENV
              value: "staging"
          ports:
            - containerPort: 8000
          resources:
            limits:
              cpu: 500m
              memory: 500Mi
            requests:
              cpu: 500m
              memory: 500Mi