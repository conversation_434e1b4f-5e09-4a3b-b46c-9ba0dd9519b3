provider "aws" {
  region = "ap-northeast-1"
  profile = "staging"
}
data "aws_iam_policy_document" "coupon-policy-doc" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII"
    ]
  }
}

resource "aws_iam_policy" "coupon_policy" {
  name        = "coupon_policy"
  description = "Access to coupon API Code"
  policy      = data.aws_iam_policy_document.coupon-policy-doc.json
}