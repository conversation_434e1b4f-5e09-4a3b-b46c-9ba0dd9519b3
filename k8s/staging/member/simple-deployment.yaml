---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: member
  namespace: staging
spec:
  selector:
    matchLabels:
      run: member
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: member
        compute-type: fargate
    spec:
      serviceAccountName: member-account
      containers:
        - name: member
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/member:latest
          env:
            - name: CONFIG_PATH
              value: "/app/config/staging.yaml"
            - name: ENV
              value: staging
          ports:
            - containerPort: 8000
          resources:
            limits:
              cpu: "1"
              memory: 1024Mi
            requests:
              cpu: "700m"
              memory: 256Mi

          startupProbe:
            httpGet:
              path: /healthcheck
              port: 8000
              scheme: HTTP
            failureThreshold: 30
            periodSeconds: 10

          livenessProbe:
            httpGet:
              path: /healthcheck
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 30
            successThreshold: 1
            failureThreshold: 3

          readinessProbe:
            httpGet:
              path: /healthcheck
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 1
            timeoutSeconds: 30
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3