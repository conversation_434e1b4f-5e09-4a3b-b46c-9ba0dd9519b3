---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: php-apache
  namespace: staging
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
spec:
  ingressClassName: alb
  rules:
    - host: felix.test.com
      http:
        paths:
          - path: /
            pathType: Exact
            backend:
              service:
                name: php-apache
                port:
                  number: 80
    - host: member.uniopen.com
      http:
        paths:
          - path: /api/token/
            pathType: Prefix
            backend:
              service:
                name: token
                port:
                  number: 8000