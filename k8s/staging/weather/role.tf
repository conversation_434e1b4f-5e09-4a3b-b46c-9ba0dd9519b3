provider "aws" {
  region = "ap-northeast-1"
  profile = "staging"
}
data "aws_iam_policy_document" "CodeBuildBasePolicy-weather-ap-northeast-1" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging_weather_auth_code-JOXCjj",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx"
    ]
  }
}

resource "aws_iam_policy" "weather_acm_policy" {
  name        = "weather_auth_code_policy"
  description = "Access to Weather API Code"
  policy      = data.aws_iam_policy_document.CodeBuildBasePolicy-weather-ap-northeast-1.json
}