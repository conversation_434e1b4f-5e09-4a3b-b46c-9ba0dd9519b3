---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: weather
  namespace: staging
spec:
  selector:
    matchLabels:
      run: weather
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: weather
    spec:
      serviceAccountName: weather-account
      nodeSelector:
        nodegroup: "services"
      tolerations:
        - key: "servicesOnly"
          operator: "Equal"
          value: "true"
          effect: "NoSchedule"
      containers:
        - name: weather
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/weather:latest
          imagePullPolicy: Always
          env:
            - name: ENV
              value: "staging"
            - name: REDIS_HOST
              value: "weather-redis-cache.4trrg4.ng.0001.apne1.cache.amazonaws.com"
            - name: REDIS_PORT
              value: "6379"
            - name: REDIS_DATABASE
              value: "0"
            - name: DB_HOST
              value: "localhost"
            - name: DB_PORT
              value: "27017"
            - name: DB_NAME
              value: "test"
            - name: DB_USER
              value: "root"
          ports:
            - containerPort: 8000
          resources:
            limits:
              cpu: 250m
              memory: 256Mi
            requests:
              cpu: 250m
              memory: 256Mi
          startupProbe:
            httpGet:
              path: /healthcheck
              port: 8000
              scheme: HTTP
            failureThreshold: 30
            periodSeconds: 10

          livenessProbe:
            httpGet:
              path: /healthcheck
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 30
            successThreshold: 1
            failureThreshold: 3

          readinessProbe:
            httpGet:
              path: /healthcheck
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 30
            timeoutSeconds: 30
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3