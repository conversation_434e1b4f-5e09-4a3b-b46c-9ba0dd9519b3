provider "aws" {
  region = "ap-northeast-1"
}

data "aws_iam_policy_document" "strapi-policy-doc" {
  statement {
    actions = [
      "s3:*",
    ]
    resources = [
      "arn:aws:s3:::uat-image",
      "arn:aws:s3:::uat-image/*"
    ]
  }
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret",
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-*"
    ]
  }
  statement {
    actions = [
      "cloudfront:CreateInvalidation"
    ]
    resources = [
      "*",
    ]
  }
}

resource "aws_iam_policy" "strapi-policy" {
  name        = "strapi-policy"
  description = "strapi policy"
  policy      = data.aws_iam_policy_document.strapi-policy-doc.json
}
