apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: strapi-secrets
  namespace: staging
spec:
  provider: aws
  secretObjects:
    - secretName: strapi-secrets-env
      type: Opaque
      data:
        - objectName: "staging-strapi-database-password"
          key: "DATABASE_PASSWORD"
        - objectName: "staging-strapi-app-keys"
          key: "APP_KEYS"
        - objectName: "staging-strapi-api-token-salt"
          key: "API_TOKEN_SALT"
        - objectName: "staging-strapi-admin-jwt-key"
          key: "ADMIN_JWT_SECRET"
        - objectName: "staging-strapi-transfer-token-salt"
          key: "TRANSFER_TOKEN_SALT"
        - objectName: "staging-strapi-jwt-secret"
          key: "JWT_SECRET"
  parameters:
    objects: |
      - objectName: "staging-strapi-database-password"
        objectType: "secretsmanager"
      - objectName: "staging-strapi-app-keys"
        objectType: "secretsmanager"
      - objectName: "staging-strapi-api-token-salt"
        objectType: "secretsmanager"
      - objectName: "staging-strapi-admin-jwt-key"
        objectType: "secretsmanager"
      - objectName: "staging-strapi-transfer-token-salt"
        objectType: "secretsmanager"
      - objectName: "staging-strapi-jwt-secret"
        objectType: "secretsmanager"
