# ========== 建立 Secrets ==========

resource "aws_secretsmanager_secret" "strapi_database_password" {
  name = "staging-strapi-database-password"
}

resource "aws_secretsmanager_secret_version" "strapi_database_password" {
  secret_id     = aws_secretsmanager_secret.strapi_database_password.id
  secret_string = "Strapi123!"
}

resource "aws_secretsmanager_secret" "strapi_app_keys" {
  name = "staging-strapi-app-keys"
}

resource "aws_secretsmanager_secret_version" "strapi_app_keys" {
  secret_id     = aws_secretsmanager_secret.strapi_app_keys.id
  secret_string = "b7N1LUuBE6qgysXzwauQTw==,2QtS6Ii4kHqGPWZCgIONhw==,tW7fo2GASplKk/tFidsjow==,Pg1PPo0euF0qCqpzPJ5gig=="
}

resource "aws_secretsmanager_secret" "strapi_api_token_salt" {
  name = "staging-strapi-api-token-salt"
}

resource "aws_secretsmanager_secret_version" "strapi_api_token_salt" {
  secret_id     = aws_secretsmanager_secret.strapi_api_token_salt.id
  secret_string = "8wC3TXT2I+60loZL2S6eKg=="
}

resource "aws_secretsmanager_secret" "strapi_admin_jwt_key" {
  name = "staging-strapi-admin-jwt-key"
}

resource "aws_secretsmanager_secret_version" "strapi_admin_jwt_key" {
  secret_id     = aws_secretsmanager_secret.strapi_admin_jwt_key.id
  secret_string = "A3ydiBJVmwcXvYx7bHbEqA=="
}

resource "aws_secretsmanager_secret" "strapi_transfer_token_salt" {
  name = "staging-strapi-transfer-token-salt"
}

resource "aws_secretsmanager_secret_version" "strapi_transfer_token_salt" {
  secret_id     = aws_secretsmanager_secret.strapi_transfer_token_salt.id
  secret_string = "MNj8eHuDEXPPXmIYiIotNQ=="
}

resource "aws_secretsmanager_secret" "strapi_jwt_secret" {
  name = "staging-strapi-jwt-secret"
}

resource "aws_secretsmanager_secret_version" "strapi_jwt_secret" {
  secret_id     = aws_secretsmanager_secret.strapi_jwt_secret.id
  secret_string = "JpreRxf19hxECiVJBg6cBwytQAUphRboE1t/qMw2N1M="
}
