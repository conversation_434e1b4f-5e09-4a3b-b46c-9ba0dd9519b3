---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: strapi
  namespace: staging
spec:
  minReadySeconds: 15
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      run: strapi
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: strapi
        compute-type: strapi
    spec:
      nodeSelector:
        nodegroup: "secrets"
      serviceAccountName: strapi-account
      containers:
        - name: strapi
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/strapi:latest
          imagePullPolicy: Always
          env:
            - name: DATABASE_CLIENT
              value: postgres
            - name: DATABASE_HOST
              value: "strapi-postgres.ct0m0u8isx79.ap-northeast-1.rds.amazonaws.com"
            - name: DATABASE_PORT
              value: "5432"
            - name: DATABASE_NAME
              value: strapidb
            - name: DATABASE_USERNAME
              value: "strapiadmin"
            - name: DATABASE_SSL
              value: "true"
            - name: DATABASE_SSL_REJECT_UNAUTHORIZED
              value: "false"
            - name: S3_BUCKET
              value: uat-image
            - name: CLOUDFRONT_DISTRIBUTION_ID
              value: E26BWP0NDGBP3T
            - name: CRON_JOB_ENABLED
              value: "true"
            - name: ADMIN_API_URL
              value: "https://admin-api.dev.uniopen.com"
            - name: CORS_ORIGIN
              value: "https://admin.dev.uniopen.com"
          envFrom:
            - secretRef:
                name: strapi-secrets-env
          volumeMounts:
            - name: secrets-store-env
              mountPath: "/mnt/secrets-store"
              readOnly: true
          ports:
            - containerPort: 1337
          resources:
            limits:
              cpu: 250m
              memory: 256Mi
            requests:
              cpu: 250m
              memory: 256Mi
      volumes:
        - name: secrets-store-env
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: "strapi-secrets"
