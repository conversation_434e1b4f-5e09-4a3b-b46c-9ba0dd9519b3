{"version": 4, "terraform_version": "1.5.7", "serial": 45, "lineage": "b4d3ce13-7065-84c0-0f85-b69d3e5d2489", "outputs": {}, "resources": [{"mode": "data", "type": "aws_iam_policy_document", "name": "strapi-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"s3:*\",\n      \"Resource\": [\n        \"arn:aws:s3:::uat-image/*\",\n        \"arn:aws:s3:::uat-image\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"cloudfront:CreateInvalidation\",\n      \"Resource\": \"*\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":\"s3:*\",\"Resource\":[\"arn:aws:s3:::uat-image/*\",\"arn:aws:s3:::uat-image\"]},{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-*\"},{\"Effect\":\"Allow\",\"Action\":\"cloudfront:CreateInvalidation\",\"Resource\":\"*\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["s3:*"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::uat-image", "arn:aws:s3:::uat-image/*"], "sid": ""}, {"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-*"], "sid": ""}, {"actions": ["cloudfront:CreateInvalidation"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "strapi-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/strapi-policy", "attachment_count": 1, "description": "strapi policy", "id": "arn:aws:iam::471112919450:policy/strapi-policy", "name": "strapi-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":\"s3:*\",\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::uat-image/*\",\"arn:aws:s3:::uat-image\"]},{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-*\"},{\"Action\":\"cloudfront:CreateInvalidation\",\"Effect\":\"Allow\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNHZTTET23T", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.strapi-policy-doc"]}]}, {"mode": "managed", "type": "aws_secretsmanager_secret", "name": "strapi_admin_jwt_key", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-admin-jwt-key-4Sh640", "description": "", "force_overwrite_replica_secret": false, "id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-admin-jwt-key-4Sh640", "kms_key_id": "", "name": "staging-strapi-admin-jwt-key", "name_prefix": "", "policy": "", "recovery_window_in_days": 30, "replica": [], "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_secretsmanager_secret", "name": "strapi_api_token_salt", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-api-token-salt-HhXNqd", "description": "", "force_overwrite_replica_secret": false, "id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-api-token-salt-HhXNqd", "kms_key_id": "", "name": "staging-strapi-api-token-salt", "name_prefix": "", "policy": "", "recovery_window_in_days": 30, "replica": [], "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_secretsmanager_secret", "name": "strapi_app_keys", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-app-keys-x2pKF6", "description": "", "force_overwrite_replica_secret": false, "id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-app-keys-x2pKF6", "kms_key_id": "", "name": "staging-strapi-app-keys", "name_prefix": "", "policy": "", "recovery_window_in_days": 30, "replica": [], "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_secretsmanager_secret", "name": "strapi_database_password", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-database-password-4hQewe", "description": "", "force_overwrite_replica_secret": false, "id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-database-password-4hQewe", "kms_key_id": "", "name": "staging-strapi-database-password", "name_prefix": "", "policy": "", "recovery_window_in_days": 30, "replica": [], "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_secretsmanager_secret", "name": "strapi_jwt_secret", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-jwt-secret-x2pKF6", "description": "", "force_overwrite_replica_secret": false, "id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-jwt-secret-x2pKF6", "kms_key_id": "", "name": "staging-strapi-jwt-secret", "name_prefix": "", "policy": "", "recovery_window_in_days": 30, "replica": [], "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_secretsmanager_secret", "name": "strapi_transfer_token_salt", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-transfer-token-salt-TUkSx1", "description": "", "force_overwrite_replica_secret": false, "id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-transfer-token-salt-TUkSx1", "kms_key_id": "", "name": "staging-strapi-transfer-token-salt", "name_prefix": "", "policy": "", "recovery_window_in_days": 30, "replica": [], "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_secretsmanager_secret_version", "name": "strapi_admin_jwt_key", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-admin-jwt-key-4Sh640", "has_secret_string_wo": null, "id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-admin-jwt-key-4Sh640|terraform-20250429071923228800000004", "secret_binary": "", "secret_id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-admin-jwt-key-4Sh640", "secret_string": "A3ydiBJVmwcXvYx7bHbEqA==", "secret_string_wo": null, "secret_string_wo_version": null, "version_id": "terraform-20250429071923228800000004", "version_stages": ["AWSCURRENT"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "secret_string"}], [{"type": "get_attr", "value": "secret_string_wo"}], [{"type": "get_attr", "value": "secret_binary"}]], "private": "bnVsbA==", "dependencies": ["aws_secretsmanager_secret.strapi_admin_jwt_key"]}]}, {"mode": "managed", "type": "aws_secretsmanager_secret_version", "name": "strapi_api_token_salt", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-api-token-salt-HhXNqd", "has_secret_string_wo": null, "id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-api-token-salt-HhXNqd|terraform-20250429071923242900000006", "secret_binary": "", "secret_id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-api-token-salt-HhXNqd", "secret_string": "8wC3TXT2I+60loZL2S6eKg==", "secret_string_wo": null, "secret_string_wo_version": null, "version_id": "terraform-20250429071923242900000006", "version_stages": ["AWSCURRENT"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "secret_string"}], [{"type": "get_attr", "value": "secret_binary"}], [{"type": "get_attr", "value": "secret_string_wo"}]], "private": "bnVsbA==", "dependencies": ["aws_secretsmanager_secret.strapi_api_token_salt"]}]}, {"mode": "managed", "type": "aws_secretsmanager_secret_version", "name": "strapi_app_keys", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-app-keys-x2pKF6", "has_secret_string_wo": null, "id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-app-keys-x2pKF6|terraform-20250429071923230000000005", "secret_binary": "", "secret_id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-app-keys-x2pKF6", "secret_string": "b7N1LUuBE6qgysXzwauQTw==,2QtS6Ii4kHqGPWZCgIONhw==,tW7fo2GASplKk/tFidsjow==,Pg1PPo0euF0qCqpzPJ5gig==", "secret_string_wo": null, "secret_string_wo_version": null, "version_id": "terraform-20250429071923230000000005", "version_stages": ["AWSCURRENT"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "secret_string_wo"}], [{"type": "get_attr", "value": "secret_string"}], [{"type": "get_attr", "value": "secret_binary"}]], "private": "bnVsbA==", "dependencies": ["aws_secretsmanager_secret.strapi_app_keys"]}]}, {"mode": "managed", "type": "aws_secretsmanager_secret_version", "name": "strapi_database_password", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-database-password-4hQewe", "has_secret_string_wo": null, "id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-database-password-4hQewe|terraform-20250429071923216300000003", "secret_binary": "", "secret_id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-database-password-4hQewe", "secret_string": "Strapi123!", "secret_string_wo": null, "secret_string_wo_version": null, "version_id": "terraform-20250429071923216300000003", "version_stages": ["AWSCURRENT"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "secret_string_wo"}], [{"type": "get_attr", "value": "secret_binary"}], [{"type": "get_attr", "value": "secret_string"}]], "private": "bnVsbA==", "dependencies": ["aws_secretsmanager_secret.strapi_database_password"]}]}, {"mode": "managed", "type": "aws_secretsmanager_secret_version", "name": "strapi_jwt_secret", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-jwt-secret-x2pKF6", "has_secret_string_wo": null, "id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-jwt-secret-x2pKF6|terraform-20250429071923209300000002", "secret_binary": "", "secret_id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-jwt-secret-x2pKF6", "secret_string": "JpreRxf19hxECiVJBg6cBwytQAUphRboE1t/qMw2N1M=", "secret_string_wo": null, "secret_string_wo_version": null, "version_id": "terraform-20250429071923209300000002", "version_stages": ["AWSCURRENT"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "secret_string_wo"}], [{"type": "get_attr", "value": "secret_string"}], [{"type": "get_attr", "value": "secret_binary"}]], "private": "bnVsbA==", "dependencies": ["aws_secretsmanager_secret.strapi_jwt_secret"]}]}, {"mode": "managed", "type": "aws_secretsmanager_secret_version", "name": "strapi_transfer_token_salt", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-transfer-token-salt-TUkSx1", "has_secret_string_wo": null, "id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-transfer-token-salt-TUkSx1|terraform-20250429071923209200000001", "secret_binary": "", "secret_id": "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-strapi-transfer-token-salt-TUkSx1", "secret_string": "MNj8eHuDEXPPXmIYiIotNQ==", "secret_string_wo": null, "secret_string_wo_version": null, "version_id": "terraform-20250429071923209200000001", "version_stages": ["AWSCURRENT"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "secret_binary"}], [{"type": "get_attr", "value": "secret_string"}], [{"type": "get_attr", "value": "secret_string_wo"}]], "private": "bnVsbA==", "dependencies": ["aws_secretsmanager_secret.strapi_transfer_token_salt"]}]}], "check_results": null}