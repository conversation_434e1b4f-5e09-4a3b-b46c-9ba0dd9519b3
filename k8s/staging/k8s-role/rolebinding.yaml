apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: staging-uniopen-serviceaccounts-binding
  namespace: staging
subjects:
- kind: ServiceAccount
  name: member-account
  namespace: staging
- kind: ServiceAccount
  name: token-service-account
  namespace: staging
- kind: ServiceAccount
  name: opinion-account
  namespace: staging
- kind: ServiceAccount
  name: ecsearch-account
  namespace: staging
- kind: ServiceAccount
  name: redirect-account
  namespace: staging
- kind: ServiceAccount
  name: admin-api-account
  namespace: staging
- kind: ServiceAccount
  name: media-account
  namespace: staging
- kind: ServiceAccount
  name: marketing-campaign-account
  namespace: staging
- kind: ServiceAccount
  name: admin-coupon-account
  namespace: staging
- kind: ServiceAccount
  name: coupon-account
  namespace: staging
- kind: ServiceAccount
  name: reservation-account
  namespace: staging
- kind: ServiceAccount
  name: admin-map-account
  namespace: staging


roleRef:
  kind: Role
  name: staging-uniopen-serviceaccounts
  apiGroup: rbac.authorization.k8s.io