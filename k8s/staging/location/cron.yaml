apiVersion: batch/v1
kind: CronJob
metadata:
  name: scale-down-location
  namespace: staging
spec:
  schedule: "0 13 * * 1-5"  # from Monday to Friday, 13:00 GMT, 21:00 GMT+8
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 0
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            name: scale-down-location
        spec:
          containers:
          - name: scale-down
            image: bitnami/kubectl:latest
            imagePullPolicy: IfNotPresent
            command:
            - /bin/sh
            - -c
            - |
              kubectl scale deployment location --replicas=0
          serviceAccountName: scaling-service-account
          restartPolicy: Never

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: scale-up-location
  namespace: staging
spec:
  schedule: "0 23 * * 0-4"  # from Sunday to Thursday, 23:00 GMT, 07:00 GMT+8
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 0
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: scale-up-location
        spec:
          containers:
          - name: scale-up
            image: bitnami/kubectl:latest
            imagePullPolicy: IfNotPresent
            command:
            - /bin/sh
            - -c
            - |
              kubectl scale deployment location --replicas=1
          serviceAccountName: scaling-service-account
          restartPolicy: Never