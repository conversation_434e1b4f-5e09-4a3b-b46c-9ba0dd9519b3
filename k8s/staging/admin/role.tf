provider "aws" {
  region = "ap-northeast-1"
  profile = "staging"
}

data "aws_iam_policy_document" "admin-service-account-policy" {
  statement {
    effect = "Allow"
    actions = [
        "appconfig:ListApplications",
        "appconfig:ListConfigurationProfiles",
        "appconfig:ListEnvironments",
        "appconfig:ListDeployments",
        "appconfig:CreateHostedConfigurationVersion",
        "appconfig:StartDeployment",
        "appconfig:StartConfigurationSession",
        "appconfig:GetLatestConfiguration"
    ]
    resources = [
      "arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb",
      "arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb/environment/ef1fq2l",
      "arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb/configurationprofile/umdqbnq",
      "arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb/environment/ef1fq2l/configuration/umdqbnq",
      "arn:aws:appconfig:ap-northeast-1:************:deploymentstrategy/*"
    ]
  }
  statement {
    effect = "Allow"
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret",
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-strapi-api-token-*"
    ]
  }
  statement {
    effect = "Allow"
    actions = [
        "appconfig:ListApplications"
    ]
    resources = [
      "arn:aws:appconfig:ap-northeast-1:************:*"
    ]
  }
}

resource "aws_iam_policy" "admin-service-account-policy" {
  name        = "admin-service-account-policy"
  description = "www access appconfig for feature flags"
  policy      = data.aws_iam_policy_document.admin-service-account-policy.json
}
