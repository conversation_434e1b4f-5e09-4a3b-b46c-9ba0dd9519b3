apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin
  namespace: staging
spec:
  minReadySeconds: 15
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      run: admin
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: admin
    spec:
      serviceAccountName: admin-account
      nodeSelector:
        nodegroup: "services"
      tolerations:
        - key: "servicesOnly"
          operator: "Equal"
          value: "true"
          effect: "NoSchedule"
      containers:
        - name: admin
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/admin:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          resources:
            limits:
              cpu: 250m
              memory: 256Mi
            requests:
              cpu: 250m
              memory: 256Mi
