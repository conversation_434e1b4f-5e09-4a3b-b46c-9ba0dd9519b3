{"version": 4, "terraform_version": "1.5.7", "serial": 35, "lineage": "3999ab08-ee32-1c3c-f5e5-112cd4f22a61", "outputs": {}, "resources": [{"mode": "data", "type": "aws_iam_policy_document", "name": "admin-service-account-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "*********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"appconfig:StartDeployment\",\n        \"appconfig:StartConfigurationSession\",\n        \"appconfig:ListEnvironments\",\n        \"appconfig:ListDeployments\",\n        \"appconfig:ListConfigurationProfiles\",\n        \"appconfig:ListApplications\",\n        \"appconfig:GetLatestConfiguration\",\n        \"appconfig:CreateHostedConfigurationVersion\"\n      ],\n      \"Resource\": [\n        \"arn:aws:appconfig:ap-northeast-1:************:deploymentstrategy/*\",\n        \"arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb/environment/ef1fq2l/configuration/umdqbnq\",\n        \"arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb/environment/ef1fq2l\",\n        \"arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb/configurationprofile/umdqbnq\",\n        \"arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": \"arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-strapi-api-token\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"appconfig:ListApplications\",\n      \"Resource\": \"arn:aws:appconfig:ap-northeast-1:************:*\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"appconfig:StartDeployment\",\"appconfig:StartConfigurationSession\",\"appconfig:ListEnvironments\",\"appconfig:ListDeployments\",\"appconfig:ListConfigurationProfiles\",\"appconfig:ListApplications\",\"appconfig:GetLatestConfiguration\",\"appconfig:CreateHostedConfigurationVersion\"],\"Resource\":[\"arn:aws:appconfig:ap-northeast-1:************:deploymentstrategy/*\",\"arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb/environment/ef1fq2l/configuration/umdqbnq\",\"arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb/environment/ef1fq2l\",\"arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb/configurationprofile/umdqbnq\",\"arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb\"]},{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":\"arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-strapi-api-token\"},{\"Effect\":\"Allow\",\"Action\":\"appconfig:ListApplications\",\"Resource\":\"arn:aws:appconfig:ap-northeast-1:************:*\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["appconfig:CreateHostedConfigurationVersion", "appconfig:GetLatestConfiguration", "appconfig:ListApplications", "appconfig:ListConfigurationProfiles", "appconfig:ListDeployments", "appconfig:ListEnvironments", "appconfig:StartConfigurationSession", "appconfig:StartDeployment"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb", "arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb/configurationprofile/umdqbnq", "arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb/environment/ef1fq2l", "arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb/environment/ef1fq2l/configuration/umdqbnq", "arn:aws:appconfig:ap-northeast-1:************:deploymentstrategy/*"], "sid": ""}, {"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-strapi-api-token"], "sid": ""}, {"actions": ["appconfig:ListApplications"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:appconfig:ap-northeast-1:************:*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "admin-service-account-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/admin-service-account-policy", "attachment_count": 1, "description": "www access appconfig for feature flags", "id": "arn:aws:iam::************:policy/admin-service-account-policy", "name": "admin-service-account-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"appconfig:StartDeployment\",\"appconfig:StartConfigurationSession\",\"appconfig:ListEnvironments\",\"appconfig:ListDeployments\",\"appconfig:ListConfigurationProfiles\",\"appconfig:ListApplications\",\"appconfig:GetLatestConfiguration\",\"appconfig:CreateHostedConfigurationVersion\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:appconfig:ap-northeast-1:************:deploymentstrategy/*\",\"arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb/environment/ef1fq2l/configuration/umdqbnq\",\"arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb/environment/ef1fq2l\",\"arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb/configurationprofile/umdqbnq\",\"arn:aws:appconfig:ap-northeast-1:************:application/6zlo7qb\"]},{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:secretsmanager:ap-northeast-1:************:secret:staging-strapi-api-token\"},{\"Action\":\"appconfig:ListApplications\",\"Effect\":\"Allow\",\"Resource\":\"arn:aws:appconfig:ap-northeast-1:************:*\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNAYLPINQFV", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.admin-service-account-policy"]}]}], "check_results": null}