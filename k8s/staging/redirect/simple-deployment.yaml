---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redirect
  namespace: staging
spec:
  selector:
    matchLabels:
      run: redirect
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: redirect
    spec:
      serviceAccountName: redirect-account
      nodeSelector:
        nodegroup: "services"
      tolerations:
        - key: "servicesOnly"
          operator: "Equal"
          value: "true"
          effect: "NoSchedule"
      terminationGracePeriodSeconds: 120
      containers:
        - name: redirect
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/redirect:latest
          env:
            - name: CONFIG_PATH
              value: "/app/config/staging.yaml"
            - name: ENV
              value: staging
          ports:
            - containerPort: 8000
          resources:
            limits:
              cpu: 500m
              memory: 500Mi
            requests:
              cpu: 500m
              memory: 500Mi

#          startupProbe:
#            httpGet:
#              path: /hello/token
#              port: 8000
#              scheme: HTTP
#            failureThreshold: 30
#            periodSeconds: 10
#
#          livenessProbe:
#            httpGet:
#              path: /hello/token
#              port: 8000
#              scheme: HTTP
#            initialDelaySeconds: 30
#            periodSeconds: 10
#            timeoutSeconds: 30
#            successThreshold: 1
#            failureThreshold: 3
#
#          readinessProbe:
#            httpGet:
#              path: /hello/token
#              port: 8000
#              scheme: HTTP
#            initialDelaySeconds: 30
#            timeoutSeconds: 30
#            periodSeconds: 10
#            successThreshold: 1
#            failureThreshold: 3