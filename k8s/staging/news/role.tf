provider "aws" {
  region = "ap-northeast-1"
  profile = "staging"
}

data "aws_iam_policy_document" "CodeBuildBasePolicy-news-ap-northeast-1" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging_news_sha_key-X9WUBi",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-id-RwqoSI",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-secret-kHy1tN"
    ]
  }
  statement {
    actions = [
      "sqs:SendMessage",
      "sqs:GetQueueUrl"
    ]
    resources = [
      "arn:aws:sqs:ap-northeast-1:471112919450:news-classification-queue",
      "arn:aws:sqs:ap-northeast-1:471112919450:counter-queue"
    ]
  }
}

resource "aws_iam_policy" "news_acm_policy" {
  name        = "staging_news_sha_key"
  description = "Access to News SHA Key"
  policy      = data.aws_iam_policy_document.CodeBuildBasePolicy-news-ap-northeast-1.json
}