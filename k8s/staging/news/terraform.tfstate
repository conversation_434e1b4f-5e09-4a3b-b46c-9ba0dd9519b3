{"version": 4, "terraform_version": "1.5.7", "serial": 35, "lineage": "1775d546-d7ee-3af0-8b9d-167b1631c00f", "outputs": {}, "resources": [{"mode": "data", "type": "aws_iam_policy_document", "name": "CodeBuildBasePolicy-news-ap-northeast-1", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": [\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging_news_sha_key-X9WUBi\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-secret-kHy1tN\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-id-RwqoSI\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"sqs:SendMessage\",\n        \"sqs:GetQueueUrl\"\n      ],\n      \"Resource\": [\n        \"arn:aws:sqs:ap-northeast-1:471112919450:news-classification-queue\",\n        \"arn:aws:sqs:ap-northeast-1:471112919450:counter-queue\"\n      ]\n    }\n  ]\n}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-id-RwqoSI", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-secret-kHy1tN", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging_news_sha_key-X9WUBi"], "sid": ""}, {"actions": ["sqs:GetQueueUrl", "sqs:SendMessage"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:sqs:ap-northeast-1:471112919450:counter-queue", "arn:aws:sqs:ap-northeast-1:471112919450:news-classification-queue"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "news_acm_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/staging_news_sha_key", "description": "Access to News SHA Key", "id": "arn:aws:iam::471112919450:policy/staging_news_sha_key", "name": "staging_news_sha_key", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging_news_sha_key-X9WUBi\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-secret-kHy1tN\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-id-RwqoSI\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Action\":[\"sqs:SendMessage\",\"sqs:GetQueueUrl\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:sqs:ap-northeast-1:471112919450:news-classification-queue\",\"arn:aws:sqs:ap-northeast-1:471112919450:counter-queue\"]}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNHPABTNXRF", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.CodeBuildBasePolicy-news-ap-northeast-1"]}]}], "check_results": null}