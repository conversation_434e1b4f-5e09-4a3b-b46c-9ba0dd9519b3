{"version": 4, "terraform_version": "1.10.4", "serial": 7, "lineage": "2e721d74-9676-ffdb-1543-52ce714ddf9f", "outputs": {}, "resources": [{"mode": "data", "type": "aws_iam_policy_document", "name": "staging_reservation_policy_doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": [\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-turnstile-secret-fCqyNS\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-inline-api-key-2krFpl\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"appconfig:StartConfigurationSession\",\n        \"appconfig:GetLatestConfiguration\"\n      ],\n      \"Resource\": [\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"kafka-cluster:WriteDataIdempotently\",\n        \"kafka-cluster:DescribeCluster\",\n        \"kafka-cluster:Connect\"\n      ],\n      \"Resource\": \"arn:aws:kafka:ap-northeast-1:471112919450:cluster/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"kafka-cluster:WriteData\",\n        \"kafka-cluster:ReadData\",\n        \"kafka-cluster:*Topic*\"\n      ],\n      \"Resource\": \"arn:aws:kafka:ap-northeast-1:471112919450:topic/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3/redirect-events\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"sqs:ReceiveMessage\",\n        \"sqs:GetQueueUrl\",\n        \"sqs:DeleteMessage\"\n      ],\n      \"Resource\": \"arn:aws:sqs:ap-northeast-1:471112919450:member_logout_queue\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"s3:PutObjectAcl\",\n        \"s3:PutObject\",\n        \"s3:GetObjectAcl\",\n        \"s3:GetObject\",\n        \"s3:DeleteObject\"\n      ],\n      \"Resource\": [\n        \"arn:aws:s3:::uat-ec-product/brand/*\",\n        \"arn:aws:s3:::uat-ec-product\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"appconfig:StartConfigurationSession\",\n        \"appconfig:GetLatestConfiguration\"\n      ],\n      \"Resource\": [\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"\n      ]\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-turnstile-secret-fCqyNS\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-inline-api-key-2krFpl\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Effect\":\"Allow\",\"Action\":[\"appconfig:StartConfigurationSession\",\"appconfig:GetLatestConfiguration\"],\"Resource\":[\"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"]},{\"Effect\":\"Allow\",\"Action\":[\"kafka-cluster:WriteDataIdempotently\",\"kafka-cluster:DescribeCluster\",\"kafka-cluster:Connect\"],\"Resource\":\"arn:aws:kafka:ap-northeast-1:471112919450:cluster/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3\"},{\"Effect\":\"Allow\",\"Action\":[\"kafka-cluster:WriteData\",\"kafka-cluster:ReadData\",\"kafka-cluster:*Topic*\"],\"Resource\":\"arn:aws:kafka:ap-northeast-1:471112919450:topic/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3/redirect-events\"},{\"Effect\":\"Allow\",\"Action\":[\"sqs:ReceiveMessage\",\"sqs:GetQueueUrl\",\"sqs:DeleteMessage\"],\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:member_logout_queue\"},{\"Effect\":\"Allow\",\"Action\":[\"s3:PutObjectAcl\",\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Resource\":[\"arn:aws:s3:::uat-ec-product/brand/*\",\"arn:aws:s3:::uat-ec-product\"]},{\"Effect\":\"Allow\",\"Action\":[\"appconfig:StartConfigurationSession\",\"appconfig:GetLatestConfiguration\"],\"Resource\":[\"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"]}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-inline-api-key-2krFpl", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-turnstile-secret-fCqyNS"], "sid": ""}, {"actions": ["appconfig:GetLatestConfiguration", "appconfig:StartConfigurationSession"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:appconfig:ap-northeast-1:471112919450:application/*", "arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*", "arn:aws:appconfig:ap-northeast-1:471112919450:environment/*"], "sid": ""}, {"actions": ["kafka-cluster:Connect", "kafka-cluster:DescribeCluster", "kafka-cluster:WriteDataIdempotently"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:kafka:ap-northeast-1:471112919450:cluster/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3"], "sid": ""}, {"actions": ["kafka-cluster:*Topic*", "kafka-cluster:ReadData", "kafka-cluster:WriteData"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:kafka:ap-northeast-1:471112919450:topic/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3/redirect-events"], "sid": ""}, {"actions": ["sqs:DeleteMessage", "sqs:GetQueueUrl", "sqs:ReceiveMessage"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:sqs:ap-northeast-1:471112919450:member_logout_queue"], "sid": ""}, {"actions": ["s3:DeleteObject", "s3:GetObject", "s3:GetObjectAcl", "s3:PutObject", "s3:PutObjectAcl"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::uat-ec-product", "arn:aws:s3:::uat-ec-product/brand/*"], "sid": ""}, {"actions": ["appconfig:GetLatestConfiguration", "appconfig:StartConfigurationSession"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:appconfig:ap-northeast-1:471112919450:application/*", "arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*", "arn:aws:appconfig:ap-northeast-1:471112919450:environment/*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "staging_reservation_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/staging-reservation-policy", "attachment_count": 1, "description": "", "id": "arn:aws:iam::471112919450:policy/staging-reservation-policy", "name": "staging-reservation-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-turnstile-secret-fCqyNS\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-inline-api-key-2krFpl\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Action\":[\"appconfig:StartConfigurationSession\",\"appconfig:GetLatestConfiguration\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"]},{\"Action\":[\"kafka-cluster:WriteDataIdempotently\",\"kafka-cluster:DescribeCluster\",\"kafka-cluster:Connect\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:kafka:ap-northeast-1:471112919450:cluster/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3\"},{\"Action\":[\"kafka-cluster:WriteData\",\"kafka-cluster:ReadData\",\"kafka-cluster:*Topic*\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:kafka:ap-northeast-1:471112919450:topic/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3/redirect-events\"},{\"Action\":[\"sqs:ReceiveMessage\",\"sqs:GetQueueUrl\",\"sqs:DeleteMessage\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:member_logout_queue\"},{\"Action\":[\"s3:PutObjectAcl\",\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::uat-ec-product/brand/*\",\"arn:aws:s3:::uat-ec-product\"]},{\"Action\":[\"appconfig:StartConfigurationSession\",\"appconfig:GetLatestConfiguration\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"]}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNDVTQMJADY", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.staging_reservation_policy_doc"]}]}], "check_results": null}