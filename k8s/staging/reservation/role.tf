provider "aws" {
  region = "ap-northeast-1"
  profile = "default"
}
data "aws_iam_policy_document" "staging_reservation_policy_doc" {
  statement {
    effect = "Allow"
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-inline-api-key-2krFpl",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-turnstile-secret-fCqyNS"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "appconfig:StartConfigurationSession",
      "appconfig:GetLatestConfiguration"
    ]
    resources = [
      "arn:aws:appconfig:ap-northeast-1:471112919450:application/*",
      "arn:aws:appconfig:ap-northeast-1:471112919450:environment/*",
      "arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "kafka-cluster:WriteDataIdempotently",
      "kafka-cluster:DescribeCluster",
      "kafka-cluster:Connect"
    ]
    resources = [
      "arn:aws:kafka:ap-northeast-1:471112919450:cluster/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "kafka-cluster:WriteData",
      "kafka-cluster:ReadData",
      "kafka-cluster:*Topic*"
    ]
    resources = [
      "arn:aws:kafka:ap-northeast-1:471112919450:topic/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3/redirect-events"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "sqs:ReceiveMessage",
      "sqs:GetQueueUrl",
      "sqs:DeleteMessage"
    ]
    resources = [
      "arn:aws:sqs:ap-northeast-1:471112919450:member_logout_queue"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:PutObjectAcl",
      "s3:GetObjectAcl",
      "s3:DeleteObject"
    ]
    resources = [
      "arn:aws:s3:::uat-ec-product/brand/*",
      "arn:aws:s3:::uat-ec-product"
    ]
  }
  statement {
    effect = "Allow"
    actions = [
      "appconfig:StartConfigurationSession",
      "appconfig:GetLatestConfiguration"
    ]
    resources = [
      "arn:aws:appconfig:ap-northeast-1:471112919450:application/*",
      "arn:aws:appconfig:ap-northeast-1:471112919450:environment/*",
      "arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*"
    ]
  }
}

resource "aws_iam_policy" "staging_reservation_policy" {
  name        = "staging-reservation-policy"
  policy      = data.aws_iam_policy_document.staging_reservation_policy_doc.json
}
