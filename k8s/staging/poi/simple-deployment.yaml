---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: poi
  namespace: staging
spec:
  selector:
    matchLabels:
      run: poi
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: poi
    spec:
      serviceAccountName: poi-account
      nodeSelector:
        nodegroup: "services"
      tolerations:
        - key: "servicesOnly"
          operator: "Equal"
          value: "true"
          effect: "NoSchedule"
      containers:
        - name: poi
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/poi:latest
          imagePullPolicy: Always
          env:
            - name: CONFIG_PATH
              value: /app/config/staging.yaml
            - name: ENV
              value: "staging"
          ports:
            - containerPort: 8000
          resources:
            limits:
              cpu: 250m
              memory: 266Mi
            requests:
              cpu: 250m
              memory: 256Mi