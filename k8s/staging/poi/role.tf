provider "aws" {
  region = "ap-northeast-1"
  profile = "staging"
}
data "aws_iam_policy_document" "poi-policy-doc" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx"
    ]
  }
}

resource "aws_iam_policy" "poi_policy" {
  name        = "poi_policy"
  description = "Access to poi API Code"
  policy      = data.aws_iam_policy_document.poi-policy-doc.json
}