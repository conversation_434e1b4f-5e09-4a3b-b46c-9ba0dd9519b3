{"version": 4, "terraform_version": "1.10.1", "serial": 15, "lineage": "46908ebc-a1a8-cfb7-d11b-b0cd1cca6e4f", "outputs": {}, "resources": [{"mode": "data", "type": "aws_iam_policy_document", "name": "staging_ecsearch_policy_doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "655630849", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": [\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-redirect-hmac-sha256-key-uL87D8\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:gcp-project-set-uniopen-sit-w8gc4C\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:dummy_pwd-vto47u\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"appconfig:StartConfigurationSession\",\n        \"appconfig:GetLatestConfiguration\"\n      ],\n      \"Resource\": [\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"kafka-cluster:WriteDataIdempotently\",\n        \"kafka-cluster:DescribeCluster\",\n        \"kafka-cluster:Connect\"\n      ],\n      \"Resource\": \"arn:aws:kafka:ap-northeast-1:471112919450:cluster/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"kafka-cluster:WriteData\",\n        \"kafka-cluster:ReadData\",\n        \"kafka-cluster:*Topic*\"\n      ],\n      \"Resource\": \"arn:aws:kafka:ap-northeast-1:471112919450:topic/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3/redirect-events\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"sqs:ReceiveMessage\",\n        \"sqs:GetQueueUrl\",\n        \"sqs:DeleteMessage\"\n      ],\n      \"Resource\": \"arn:aws:sqs:ap-northeast-1:471112919450:member_logout_queue\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"s3:PutObjectAcl\",\n        \"s3:PutObject\",\n        \"s3:GetObjectAcl\",\n        \"s3:GetObject\",\n        \"s3:DeleteObject\"\n      ],\n      \"Resource\": [\n        \"arn:aws:s3:::uat-ec-product/brand/*\",\n        \"arn:aws:s3:::uat-ec-product\"\n      ]\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-redirect-hmac-sha256-key-uL87D8\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:gcp-project-set-uniopen-sit-w8gc4C\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:dummy_pwd-vto47u\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Effect\":\"Allow\",\"Action\":[\"appconfig:StartConfigurationSession\",\"appconfig:GetLatestConfiguration\"],\"Resource\":[\"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"]},{\"Effect\":\"Allow\",\"Action\":[\"kafka-cluster:WriteDataIdempotently\",\"kafka-cluster:DescribeCluster\",\"kafka-cluster:Connect\"],\"Resource\":\"arn:aws:kafka:ap-northeast-1:471112919450:cluster/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3\"},{\"Effect\":\"Allow\",\"Action\":[\"kafka-cluster:WriteData\",\"kafka-cluster:ReadData\",\"kafka-cluster:*Topic*\"],\"Resource\":\"arn:aws:kafka:ap-northeast-1:471112919450:topic/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3/redirect-events\"},{\"Effect\":\"Allow\",\"Action\":[\"sqs:ReceiveMessage\",\"sqs:GetQueueUrl\",\"sqs:DeleteMessage\"],\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:member_logout_queue\"},{\"Effect\":\"Allow\",\"Action\":[\"s3:PutObjectAcl\",\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Resource\":[\"arn:aws:s3:::uat-ec-product/brand/*\",\"arn:aws:s3:::uat-ec-product\"]}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:dummy_pwd-vto47u", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:gcp-project-set-uniopen-sit-w8gc4C", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-redirect-hmac-sha256-key-uL87D8"], "sid": ""}, {"actions": ["appconfig:GetLatestConfiguration", "appconfig:StartConfigurationSession"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:appconfig:ap-northeast-1:471112919450:application/*", "arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*", "arn:aws:appconfig:ap-northeast-1:471112919450:environment/*"], "sid": ""}, {"actions": ["kafka-cluster:Connect", "kafka-cluster:DescribeCluster", "kafka-cluster:WriteDataIdempotently"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:kafka:ap-northeast-1:471112919450:cluster/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3"], "sid": ""}, {"actions": ["kafka-cluster:*Topic*", "kafka-cluster:ReadData", "kafka-cluster:WriteData"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:kafka:ap-northeast-1:471112919450:topic/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3/redirect-events"], "sid": ""}, {"actions": ["sqs:DeleteMessage", "sqs:GetQueueUrl", "sqs:ReceiveMessage"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:sqs:ap-northeast-1:471112919450:member_logout_queue"], "sid": ""}, {"actions": ["s3:DeleteObject", "s3:GetObject", "s3:GetObjectAcl", "s3:PutObject", "s3:PutObjectAcl"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::uat-ec-product", "arn:aws:s3:::uat-ec-product/brand/*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "uat_ecsearch_lambda_policy_doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": [\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-redirect-hmac-sha256-key-8jUfGM\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-image-hmac-sha256-key-z8OP5M\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-gcp-project-set-uniopen-sit-ZAM46H\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-docdb-password-0R6Oi4\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-cache-auth-token-lAZpf5\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-auth-jwt-secret-token-server-R7Y2Bq\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"appconfig:StartConfigurationSession\",\n        \"appconfig:GetLatestConfiguration\"\n      ],\n      \"Resource\": [\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"ec2:DescribeNetworkInterfaces\",\n        \"ec2:DescribeInstances\",\n        \"ec2:DeleteNetworkInterface\",\n        \"ec2:CreateNetworkInterface\",\n        \"ec2:AttachNetworkInterface\",\n        \"autoscaling:CompleteLifecycleAction\"\n      ],\n      \"Resource\": \"*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"s3:PutObjectAcl\",\n        \"s3:PutObject\",\n        \"s3:GetObjectAcl\",\n        \"s3:GetObject\"\n      ],\n      \"Resource\": [\n        \"arn:aws:s3:::uniopen-uat-image/category/*\",\n        \"arn:aws:s3:::uat-ec-product/search_source/*\",\n        \"arn:aws:s3:::uat-ec-product/manticore_source/*\",\n        \"arn:aws:s3:::uat-ec-product/brand/*\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"s3:DeleteObject\",\n      \"Resource\": \"arn:aws:s3:::uat-ec-product/manticore_source/*\"\n    },\n    {\n      \"Sid\": \"AllowSQSPermissions\",\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"sqs:ReceiveMessage\",\n        \"sqs:GetQueueAttributes\",\n        \"sqs:DeleteMessage\",\n        \"sqs:ChangeMessageVisibility\"\n      ],\n      \"Resource\": [\n        \"arn:aws:sqs:ap-northeast-1:471112919450:uat-ec-data-scheduler-manticore-importer-queue\",\n        \"arn:aws:sqs:ap-northeast-1:471112919450:uat-ec-data-scheduler-google-search-importer-queue\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"logs:PutLogEvents\",\n        \"logs:CreateLogStream\",\n        \"logs:CreateLogGroup\"\n      ],\n      \"Resource\": [\n        \"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-facet-warmup:log-stream:*\",\n        \"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-facet-warmup\",\n        \"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-manticore-importer:log-stream:*\",\n        \"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-google-search-importer:log-stream:*\",\n        \"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-google-search-importer\",\n        \"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-exporter:log-stream:*\",\n        \"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-exporter\",\n        \"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-category:log-stream:*\",\n        \"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-category\",\n        \"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-brand-standardizer:log-stream:*\",\n        \"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-brand-standardizer\"\n      ]\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-redirect-hmac-sha256-key-8jUfGM\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-image-hmac-sha256-key-z8OP5M\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-gcp-project-set-uniopen-sit-ZAM46H\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-docdb-password-0R6Oi4\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-cache-auth-token-lAZpf5\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-auth-jwt-secret-token-server-R7Y2Bq\"]},{\"Effect\":\"Allow\",\"Action\":[\"appconfig:StartConfigurationSession\",\"appconfig:GetLatestConfiguration\"],\"Resource\":[\"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"]},{\"Effect\":\"Allow\",\"Action\":[\"ec2:DescribeNetworkInterfaces\",\"ec2:DescribeInstances\",\"ec2:DeleteNetworkInterface\",\"ec2:CreateNetworkInterface\",\"ec2:AttachNetworkInterface\",\"autoscaling:CompleteLifecycleAction\"],\"Resource\":\"*\"},{\"Effect\":\"Allow\",\"Action\":[\"s3:PutObjectAcl\",\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\"],\"Resource\":[\"arn:aws:s3:::uniopen-uat-image/category/*\",\"arn:aws:s3:::uat-ec-product/search_source/*\",\"arn:aws:s3:::uat-ec-product/manticore_source/*\",\"arn:aws:s3:::uat-ec-product/brand/*\"]},{\"Effect\":\"Allow\",\"Action\":\"s3:DeleteObject\",\"Resource\":\"arn:aws:s3:::uat-ec-product/manticore_source/*\"},{\"Sid\":\"AllowSQSPermissions\",\"Effect\":\"Allow\",\"Action\":[\"sqs:ReceiveMessage\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\",\"sqs:ChangeMessageVisibility\"],\"Resource\":[\"arn:aws:sqs:ap-northeast-1:471112919450:uat-ec-data-scheduler-manticore-importer-queue\",\"arn:aws:sqs:ap-northeast-1:471112919450:uat-ec-data-scheduler-google-search-importer-queue\"]},{\"Effect\":\"Allow\",\"Action\":[\"logs:PutLogEvents\",\"logs:CreateLogStream\",\"logs:CreateLogGroup\"],\"Resource\":[\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-facet-warmup:log-stream:*\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-facet-warmup\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-manticore-importer:log-stream:*\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-google-search-importer:log-stream:*\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-google-search-importer\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-exporter:log-stream:*\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-exporter\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-category:log-stream:*\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-category\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-brand-standardizer:log-stream:*\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-brand-standardizer\"]}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-auth-jwt-secret-token-server-R7Y2Bq", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-cache-auth-token-lAZpf5", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-docdb-password-0R6Oi4", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-gcp-project-set-uniopen-sit-ZAM46H", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-image-hmac-sha256-key-z8OP5M", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-redirect-hmac-sha256-key-8jUfGM"], "sid": ""}, {"actions": ["appconfig:GetLatestConfiguration", "appconfig:StartConfigurationSession"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:appconfig:ap-northeast-1:471112919450:application/*", "arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*", "arn:aws:appconfig:ap-northeast-1:471112919450:environment/*"], "sid": ""}, {"actions": ["autoscaling:CompleteLifecycleAction", "ec2:AttachNetworkInterface", "ec2:CreateNetworkInterface", "ec2:DeleteNetworkInterface", "ec2:DescribeInstances", "ec2:DescribeNetworkInterfaces"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}, {"actions": ["s3:GetObject", "s3:GetObjectAcl", "s3:PutObject", "s3:PutObjectAcl"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::uat-ec-product/brand/*", "arn:aws:s3:::uat-ec-product/manticore_source/*", "arn:aws:s3:::uat-ec-product/search_source/*", "arn:aws:s3:::uniopen-uat-image/category/*"], "sid": ""}, {"actions": ["s3:DeleteObject"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::uat-ec-product/manticore_source/*"], "sid": ""}, {"actions": ["sqs:ChangeMessageVisibility", "sqs:DeleteMessage", "sqs:Get<PERSON>ueueAttributes", "sqs:ReceiveMessage"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:sqs:ap-northeast-1:471112919450:uat-ec-data-scheduler-google-search-importer-queue", "arn:aws:sqs:ap-northeast-1:471112919450:uat-ec-data-scheduler-manticore-importer-queue"], "sid": "AllowSQSPermissions"}, {"actions": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-brand-standardizer", "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-brand-standardizer:log-stream:*", "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-category", "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-category:log-stream:*", "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-exporter", "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-exporter:log-stream:*", "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-google-search-importer", "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-google-search-importer:log-stream:*", "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-manticore-importer:log-stream:*", "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-facet-warmup", "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-facet-warmup:log-stream:*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "staging_ecsearch_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/staging-ecsearch-policy", "attachment_count": 2, "description": "", "id": "arn:aws:iam::471112919450:policy/staging-ecsearch-policy", "name": "staging-ecsearch-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-redirect-hmac-sha256-key-uL87D8\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:gcp-project-set-uniopen-sit-w8gc4C\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:dummy_pwd-vto47u\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Action\":[\"appconfig:StartConfigurationSession\",\"appconfig:GetLatestConfiguration\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"]},{\"Action\":[\"kafka-cluster:WriteDataIdempotently\",\"kafka-cluster:DescribeCluster\",\"kafka-cluster:Connect\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:kafka:ap-northeast-1:471112919450:cluster/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3\"},{\"Action\":[\"kafka-cluster:WriteData\",\"kafka-cluster:ReadData\",\"kafka-cluster:*Topic*\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:kafka:ap-northeast-1:471112919450:topic/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3/redirect-events\"},{\"Action\":[\"sqs:ReceiveMessage\",\"sqs:GetQueueUrl\",\"sqs:DeleteMessage\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:member_logout_queue\"},{\"Action\":[\"s3:PutObjectAcl\",\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::uat-ec-product/brand/*\",\"arn:aws:s3:::uat-ec-product\"]}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNPBUD3FS6C", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.staging_ecsearch_policy_doc"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "uat_ecsearch_lambda_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/uat-ecsearch-lambda", "attachment_count": 1, "description": "The IAM policy for ecsearch lambda", "id": "arn:aws:iam::471112919450:policy/uat-ecsearch-lambda", "name": "uat-ecsearch-lambda", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-redirect-hmac-sha256-key-8jUfGM\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-image-hmac-sha256-key-z8OP5M\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-gcp-project-set-uniopen-sit-ZAM46H\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-docdb-password-0R6Oi4\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-cache-auth-token-lAZpf5\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-auth-jwt-secret-token-server-R7Y2Bq\"]},{\"Action\":[\"appconfig:StartConfigurationSession\",\"appconfig:GetLatestConfiguration\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"]},{\"Action\":[\"ec2:DescribeNetworkInterfaces\",\"ec2:DescribeInstances\",\"ec2:DeleteNetworkInterface\",\"ec2:CreateNetworkInterface\",\"ec2:AttachNetworkInterface\",\"autoscaling:CompleteLifecycleAction\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"s3:PutObjectAcl\",\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::uniopen-uat-image/category/*\",\"arn:aws:s3:::uat-ec-product/search_source/*\",\"arn:aws:s3:::uat-ec-product/manticore_source/*\",\"arn:aws:s3:::uat-ec-product/brand/*\"]},{\"Action\":\"s3:DeleteObject\",\"Effect\":\"Allow\",\"Resource\":\"arn:aws:s3:::uat-ec-product/manticore_source/*\"},{\"Action\":[\"sqs:ReceiveMessage\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\",\"sqs:ChangeMessageVisibility\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:sqs:ap-northeast-1:471112919450:uat-ec-data-scheduler-manticore-importer-queue\",\"arn:aws:sqs:ap-northeast-1:471112919450:uat-ec-data-scheduler-google-search-importer-queue\"],\"Sid\":\"AllowSQSPermissions\"},{\"Action\":[\"logs:PutLogEvents\",\"logs:CreateLogStream\",\"logs:CreateLogGroup\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-facet-warmup:log-stream:*\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-facet-warmup\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-manticore-importer:log-stream:*\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-google-search-importer:log-stream:*\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-google-search-importer\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-exporter:log-stream:*\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-exporter\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-category:log-stream:*\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-category\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-brand-standardizer:log-stream:*\",\"arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-brand-standardizer\"]}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNM7Y7QCRLF", "tags": {"environment": "UAT", "organization": "PCSC", "project": "UniOpen", "terraform": "true"}, "tags_all": {"environment": "UAT", "organization": "PCSC", "project": "UniOpen", "terraform": "true"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.uat_ecsearch_lambda_policy_doc"]}]}], "check_results": null}