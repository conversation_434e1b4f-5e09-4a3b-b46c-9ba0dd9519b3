provider "aws" {
  region = "ap-northeast-1"
  profile = "default"
}
data "aws_iam_policy_document" "staging_ecsearch_policy_doc" {
  statement {
    effect = "Allow"
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-redirect-hmac-sha256-key-uL87D8",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:gcp-project-set-uniopen-sit-w8gc4C",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:dummy_pwd-vto47u"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "appconfig:StartConfigurationSession",
      "appconfig:GetLatestConfiguration"
    ]
    resources = [
      "arn:aws:appconfig:ap-northeast-1:471112919450:application/*",
      "arn:aws:appconfig:ap-northeast-1:471112919450:environment/*",
      "arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "kafka-cluster:WriteDataIdempotently",
      "kafka-cluster:DescribeCluster",
      "kafka-cluster:Connect"
    ]
    resources = [
      "arn:aws:kafka:ap-northeast-1:471112919450:cluster/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "kafka-cluster:WriteData",
      "kafka-cluster:ReadData",
      "kafka-cluster:*Topic*"
    ]
    resources = [
      "arn:aws:kafka:ap-northeast-1:471112919450:topic/uniopen-uat-msk-instance/a154040c-e75d-44f5-a04d-c98c5b361fe6-3/redirect-events"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "sqs:ReceiveMessage",
      "sqs:GetQueueUrl",
      "sqs:DeleteMessage"
    ]
    resources = [
      "arn:aws:sqs:ap-northeast-1:471112919450:member_logout_queue"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:PutObjectAcl",
      "s3:GetObjectAcl",
      "s3:DeleteObject"
    ]
    resources = [
      "arn:aws:s3:::uat-ec-product/brand/*",
      "arn:aws:s3:::uat-ec-product"
    ]
  }
}

resource "aws_iam_policy" "staging_ecsearch_policy" {
  name        = "staging-ecsearch-policy"
  policy      = data.aws_iam_policy_document.staging_ecsearch_policy_doc.json
}

data "aws_iam_policy_document" "uat_ecsearch_lambda_policy_doc" {
  # SecretsManager 權限
  statement {
    effect = "Allow"
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-redirect-hmac-sha256-key-8jUfGM",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-image-hmac-sha256-key-z8OP5M",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-gcp-project-set-uniopen-sit-ZAM46H",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-docdb-password-0R6Oi4",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-cache-auth-token-lAZpf5",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-auth-jwt-secret-token-server-R7Y2Bq"
    ]
  }

  # AppConfig 權限
  statement {
    effect = "Allow"
    actions = [
      "appconfig:StartConfigurationSession",
      "appconfig:GetLatestConfiguration"
    ]
    resources = [
      "arn:aws:appconfig:ap-northeast-1:471112919450:application/*",
      "arn:aws:appconfig:ap-northeast-1:471112919450:environment/*",
      "arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*"
    ]
  }

  # EC2 + AutoScaling 權限
  statement {
    effect = "Allow"
    actions = [
      "ec2:DescribeNetworkInterfaces",
      "ec2:DescribeInstances",
      "ec2:DeleteNetworkInterface",
      "ec2:CreateNetworkInterface",
      "ec2:AttachNetworkInterface",
      "autoscaling:CompleteLifecycleAction"
    ]
    resources = ["*"]
  }

  # S3 權限
  statement {
    effect = "Allow"
    actions = [
      "s3:PutObjectAcl",
      "s3:PutObject",
      "s3:GetObjectAcl",
      "s3:GetObject"
    ]
    resources = [
      "arn:aws:s3:::uniopen-uat-image/category/*",
      "arn:aws:s3:::uat-ec-product/search_source/*",
      "arn:aws:s3:::uat-ec-product/manticore_source/*",
      "arn:aws:s3:::uat-ec-product/brand/*"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "s3:DeleteObject"
    ]
    resources = [
      "arn:aws:s3:::uat-ec-product/manticore_source/*"
    ]
  }

  # SQS 權限（含 Sid）
  statement {
    sid    = "AllowSQSPermissions"
    effect = "Allow"
    actions = [
      "sqs:ReceiveMessage",
      "sqs:GetQueueAttributes",
      "sqs:DeleteMessage",
      "sqs:ChangeMessageVisibility"
    ]
    resources = [
      "arn:aws:sqs:ap-northeast-1:471112919450:uat-ec-data-scheduler-manticore-importer-queue",
      "arn:aws:sqs:ap-northeast-1:471112919450:uat-ec-data-scheduler-google-search-importer-queue"
    ]
  }

  # CloudWatch Logs 權限
  statement {
    effect = "Allow"
    actions = [
      "logs:PutLogEvents",
      "logs:CreateLogStream",
      "logs:CreateLogGroup"
    ]
    resources = [
      "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-facet-warmup:log-stream:*",
      "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-facet-warmup",
      "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-manticore-importer:log-stream:*",
      "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-google-search-importer:log-stream:*",
      "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-google-search-importer",
      "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-exporter:log-stream:*",
      "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-exporter",
      "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-category:log-stream:*",
      "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-data-scheduler-category",
      "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-brand-standardizer:log-stream:*",
      "arn:aws:logs:ap-northeast-1:471112919450:log-group:/aws/lambda/uat-ec-brand-standardizer"
    ]
  }
}

resource "aws_iam_policy" "uat_ecsearch_lambda_policy" {
  name   = "uat-ecsearch-lambda"
  description = "The IAM policy for ecsearch lambda"
  policy = data.aws_iam_policy_document.uat_ecsearch_lambda_policy_doc.json
  tags = {
    environment  = "UAT"
    organization = "PCSC"
    project      = "UniOpen"
    terraform    = "true"
  }
}