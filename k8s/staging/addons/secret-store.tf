resource "helm_release" "csi_driver" {
  name       = "csi-secrets-store"
  namespace  = "kube-system"
  repository = "https://kubernetes-sigs.github.io/secrets-store-csi-driver/charts"
  chart      = "secrets-store-csi-driver"

  set {
    name  = "syncSecret.enabled"
    value = "true"
  }

  /*set {
    name  = "nodeSelector.eks.amazonaws.com/compute-type"
    value = "strapi"
  }

  set {
    name  = "tolerations[0].key"
    value = "node-role.kubernetes.io/control-plane"
  }*/

  set {
    name  = "nodeSelector.eks/nodegroup"
    value = "strapi"
  }
}

resource "null_resource" "aws_provider" {
  provisioner "local-exec" {
    command = "kubectl apply -f https://raw.githubusercontent.com/aws/secrets-store-csi-driver-provider-aws/main/deployment/aws-provider-installer.yaml"
  }
}