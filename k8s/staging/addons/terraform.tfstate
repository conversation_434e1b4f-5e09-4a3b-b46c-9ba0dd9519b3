{"version": 4, "terraform_version": "1.8.4", "serial": 185, "lineage": "a9b3c59e-f3a9-71b5-8e2e-4110d0bc2d8a", "outputs": {}, "resources": [{"mode": "data", "type": "aws_eks_cluster", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"access_config": [{"authentication_mode": "API_AND_CONFIG_MAP", "bootstrap_cluster_creator_admin_permissions": true}], "arn": "arn:aws:eks:ap-northeast-1:471112919450:cluster/uniopen", "certificate_authority": [{"data": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJZkJPSFNGYlBDeUF3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TkRBeU1UVXhORE0yTkRoYUZ3MHpOREF5TVRJeE5EUXhORGhhTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUUN6NzFEa0plZCtJT2hDVm9XbWRaTTF1VVJhdTB4NkJMaCtpNUVvOGo2WStWcGpKT2pHZDRZcVF1ZFYKcHU4dXVHdDdxa1h5NVVESEJsUTl0dUNDOGpUeVI5VWVlMlNFUEtGOUp3Vkl6K2YwWFBPQjhKR2FkbzJsTWZRKwpXblV4NC9oazVBUnErRnE2R2lXWmEvYmJIcCttM3VMY3FJU2F2OEFYU3pEMHZMZjBGSXNuYldUa1BacndiNTBtCjVjbWRaZ214NlhrK054NnhtVVV4S1krR2I0WVZVYnpXbUFkSzJaQ1dlMEYxYUtybWdsVzZNV2h1T2NybUpCU2wKeFlHUUtIMXhiQ1ZGMHpWazFxQkswTUxUZzVLdDBNODNlclNmeTJDYlEwSFFnTnpSemN4bkNPOU1DeDhsR3UxNwozZWhlZFRVR3U0OTBjcFNlVzh6WUxLb1BPZUZCQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJRM2lDSVFpdjU4Nk1xSW1URFRxUHI4MytrQ1dEQVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQ3ByZFdicmRxZAp0NzhiaDZiWFd0dG0yZ2QyWDQwRUtycktJNHc2T3VOZ2JPaEhUbWxGakpjOHBKb05NdGhJQ2F4V0ZHVnRhQ2p0ClN1TGN3OWhiT2FLcVFmUHpaTzlmWmp1Wk1uM2RSSmNYTjQ2QnlEUmhhQ25YaFZnTUlsUThqcUJ1Z3J6WmFBMncKRVpNemNrZEkrVkJZQldRZ0MxTURqc2FKa1Flc1plQ1AzOHRqQWw0WEJMdGRBRXoyRDYwQjdXSWlVMVQ2RHRCYQo1cDZhZVBPVzZzejVlZ3h1T1VYMWJRV2hkbE5XM3NiNEN0MzhMUE1zWVdVdGxTTnhabWp1eTd5ZDl0L3RVQ1k3CkpEeWZuWlBvQWV3WTkzd2xOc01FL21XUjlTSUtPZHM1ck5TbEd4d2wraWRxck1mZ1BUMTVYYjJiYVZYalpLdjYKZ29Qb2JtWGpkWU1FCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}], "cluster_id": null, "compute_config": [], "created_at": "2024-02-15T14:37:01Z", "enabled_cluster_log_types": [], "endpoint": "https://B3DE07E3C89D23D8EC6AA8DFD0272007.gr7.ap-northeast-1.eks.amazonaws.com", "id": "uniopen", "identity": [{"oidc": [{"issuer": "https://oidc.eks.ap-northeast-1.amazonaws.com/id/B3DE07E3C89D23D8EC6AA8DFD0272007"}]}], "kubernetes_network_config": [{"elastic_load_balancing": [], "ip_family": "ipv4", "service_ipv4_cidr": "**********/16", "service_ipv6_cidr": ""}], "name": "uniopen", "outpost_config": [], "platform_version": "eks.9", "remote_network_config": [], "role_arn": "arn:aws:iam::471112919450:role/eks-cluster-uniopen", "status": "ACTIVE", "storage_config": [], "tags": {}, "upgrade_policy": [{"support_type": "EXTENDED"}], "version": "1.32", "vpc_config": [{"cluster_security_group_id": "sg-0cd9918593e78b46b", "endpoint_private_access": true, "endpoint_public_access": true, "public_access_cidrs": ["************/32", "**************/29", "*************/32", "************/29", "*************/32"], "security_group_ids": ["sg-065ff73beb8fd069c"], "subnet_ids": ["subnet-007a652180de503c6", "subnet-09106d152651ed307", "subnet-0cdae13b1f4b1e19b", "subnet-0de9a13ff206133e6"], "vpc_id": "vpc-0b34906e9113590e4"}], "zonal_shift_config": []}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_eks_cluster_auth", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "uniopen", "name": "uniopen", "token": "k8s-aws-v1.aHR0cHM6Ly9zdHMuYXAtbm9ydGhlYXN0LTEuYW1hem9uYXdzLmNvbS8_QWN0aW9uPUdldENhbGxlcklkZW50aXR5JlZlcnNpb249MjAxMS0wNi0xNSZYLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFXM01FRVZHTkFFQURaSkgzJTJGMjAyNTA1MjglMkZhcC1ub3J0aGVhc3QtMSUyRnN0cyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjUwNTI4VDEzMDEwMFomWC1BbXotRXhwaXJlcz0wJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCUzQngtazhzLWF3cy1pZCZYLUFtei1TaWduYXR1cmU9MzUyYjBiOGEwNWQwYWMzNTJkMWE2ZTNmMTc0NGQ5MjkwMGQxNzQ3MTZjNDJlNWJhZjI2YTQxZWU0NjhjYWEwMQ"}, "sensitive_attributes": [[{"type": "get_attr", "value": "token"}]]}]}, {"mode": "managed", "type": "helm_release", "name": "csi_driver", "provider": "provider[\"registry.terraform.io/hashicorp/helm\"]", "instances": [{"status": "tainted", "schema_version": 1, "attributes": {"atomic": false, "chart": "secrets-store-csi-driver", "cleanup_on_fail": false, "create_namespace": false, "dependency_update": false, "description": null, "devel": null, "disable_crd_hooks": false, "disable_openapi_validation": false, "disable_webhooks": false, "force_update": false, "id": "csi-secrets-store", "keyring": null, "lint": false, "manifest": null, "max_history": 0, "metadata": [{"app_version": "1.5.1", "chart": "secrets-store-csi-driver", "first_deployed": **********, "last_deployed": **********, "name": "csi-secrets-store", "namespace": "kube-system", "notes": "The Secrets Store CSI Driver is getting deployed to your cluster.\n\nTo verify that Secrets Store CSI Driver has started, run:\n\n  kubectl --namespace=kube-system get pods -l \"app=secrets-store-csi-driver\"\n\nNow you can follow these steps https://secrets-store-csi-driver.sigs.k8s.io/getting-started/usage.html\nto create a SecretProviderClass resource, and a deployment using the SecretProviderClass.\n", "revision": 1, "values": "{\"nodeSelector\":{\"eks/nodegroup\":\"strapi\"},\"syncSecret\":{\"enabled\":true}}", "version": "1.5.1"}], "name": "csi-secrets-store", "namespace": "kube-system", "pass_credentials": false, "postrender": [], "recreate_pods": false, "render_subchart_notes": true, "replace": false, "repository": "https://kubernetes-sigs.github.io/secrets-store-csi-driver/charts", "repository_ca_file": null, "repository_cert_file": null, "repository_key_file": null, "repository_password": null, "repository_username": null, "reset_values": false, "reuse_values": false, "set": [{"name": "nodeSelector.eks/nodegroup", "type": "", "value": "strapi"}, {"name": "syncSecret.enabled", "type": "", "value": "true"}], "set_list": [], "set_sensitive": [], "skip_crds": false, "status": "failed", "timeout": 300, "upgrade_install": null, "values": null, "verify": false, "version": "1.5.1", "wait": true, "wait_for_jobs": false}, "sensitive_attributes": [[{"type": "get_attr", "value": "repository_password"}]], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}, {"mode": "managed", "type": "null_resource", "name": "aws_provider", "provider": "provider[\"registry.terraform.io/hashicorp/null\"]", "instances": [{"schema_version": 0, "attributes": {"id": "3662683107584993145", "triggers": null}, "sensitive_attributes": []}]}], "check_results": null}