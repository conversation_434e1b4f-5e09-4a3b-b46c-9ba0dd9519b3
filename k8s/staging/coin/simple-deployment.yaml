---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: coin
  namespace: staging
spec:
  selector:
    matchLabels:
      run: coin
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: coin
    spec:
      serviceAccountName: coin-account
      nodeSelector:
        nodegroup: "services"
      tolerations:
        - key: "servicesOnly"
          operator: "Equal"
          value: "true"
          effect: "NoSchedule"
      containers:
        - name: coin
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/coin:latest
          imagePullPolicy: Always
          env:
            - name: CONFIG_PATH
              value: /app/config/staging.yaml
            - name: ENV
              value: "staging"
          ports:
            - containerPort: 8000
          resources:
            limits:
              cpu: 250m
              memory: 256Mi
            requests:
              cpu: 250m
              memory: 256Mi
          startupProbe:
            httpGet:
              path: /healthcheck
              port: 8000
              scheme: HTTP
            failureThreshold: 3
            periodSeconds: 10
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthcheck
              port: 8000
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthcheck
              port: 8000
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1