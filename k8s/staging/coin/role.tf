provider "aws" {
  region = "ap-northeast-1"
  profile = "staging"
}
data "aws_iam_policy_document" "coin-policy-doc" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx"
    ]
  }
}

resource "aws_iam_policy" "coin_policy" {
  name        = "coin_policy"
  description = "Access to Coin API Code"
  policy      = data.aws_iam_policy_document.coin-policy-doc.json
}