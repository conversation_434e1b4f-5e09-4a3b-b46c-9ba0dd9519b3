{"version": 4, "terraform_version": "1.10.3", "serial": 7, "lineage": "24cd2ced-52fc-74f4-ccc6-de0954790579", "outputs": {}, "resources": [{"mode": "data", "type": "aws_iam_policy_document", "name": "admin-coupon-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": [\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"s3:PutObject\",\n        \"s3:ListBucket\",\n        \"s3:GetObject\",\n        \"s3:DeleteObject\"\n      ],\n      \"Resource\": [\n        \"arn:aws:s3:::uat-image/private/coupons/*\",\n        \"arn:aws:s3:::uat-image\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"sqs:SendMessage\",\n        \"sqs:ReceiveMessage\",\n        \"sqs:GetQueueUrl\",\n        \"sqs:GetQueueAttributes\",\n        \"sqs:DeleteMessage\"\n      ],\n      \"Resource\": \"arn:aws:sqs:ap-northeast-1:471112919450:coupon-generate-queue.fifo\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Effect\":\"Allow\",\"Action\":[\"s3:PutObject\",\"s3:ListBucket\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Resource\":[\"arn:aws:s3:::uat-image/private/coupons/*\",\"arn:aws:s3:::uat-image\"]},{\"Effect\":\"Allow\",\"Action\":[\"sqs:SendMessage\",\"sqs:ReceiveMessage\",\"sqs:GetQueueUrl\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\"],\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:coupon-generate-queue.fifo\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx"], "sid": ""}, {"actions": ["s3:DeleteObject", "s3:GetObject", "s3:ListBucket", "s3:PutObject"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::uat-image", "arn:aws:s3:::uat-image/private/coupons/*"], "sid": ""}, {"actions": ["sqs:DeleteMessage", "sqs:Get<PERSON>ueueAttributes", "sqs:GetQueueUrl", "sqs:ReceiveMessage", "sqs:SendMessage"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:sqs:ap-northeast-1:471112919450:coupon-generate-queue.fifo"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "admin-coupon_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/admin-coupon_policy", "attachment_count": 1, "description": "Access to admin-coupon API Code", "id": "arn:aws:iam::471112919450:policy/admin-coupon_policy", "name": "admin-coupon_policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Action\":[\"s3:PutObject\",\"s3:ListBucket\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::uat-image/private/coupons/*\",\"arn:aws:s3:::uat-image\"]},{\"Action\":[\"sqs:SendMessage\",\"sqs:ReceiveMessage\",\"sqs:GetQueueUrl\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:coupon-generate-queue.fifo\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNENJWPR5JM", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.admin-coupon-policy-doc"]}]}], "check_results": null}