---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: picasso
  namespace: staging
spec:
  selector:
    matchLabels:
      run: picasso
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: picasso
        compute-type: fargate
    spec:
      serviceAccountName: picasso-account
      containers:
        - name: picasso
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/picasso:latest
          env:
            - name: ENV
              value: staging
          ports:
            - containerPort: 8080
          resources:
            limits:
              cpu: 1024m
              memory: 1024Mi
            requests:
              cpu: 1024m
              memory: 1024Mi

          startupProbe:
            httpGet:
              path: /api/picasso/health
              port: 8080
              scheme: HTTP
            failureThreshold: 30
            periodSeconds: 10

          livenessProbe:
            httpGet:
              path: /api/picasso/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 30
            successThreshold: 1
            failureThreshold: 3

          readinessProbe:
            httpGet:
              path: /api/picasso/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            timeoutSeconds: 30
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3