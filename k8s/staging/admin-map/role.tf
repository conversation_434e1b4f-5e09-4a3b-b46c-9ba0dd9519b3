provider "aws" {
  region  = "ap-northeast-1"
  profile = "staging"
}

data "aws_iam_policy_document" "admin-map-policy-doc" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII"
    ]
  }

  statement {
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:ListBucket",
      "s3:DeleteObject"
    ]
    resources = [
      "arn:aws:s3:::uat-image/static_resources/store_campaigns/*",
      "arn:aws:s3:::uat-image"
    ]
  }
  statement {
    actions = [
      "cloudfront:CreateInvalidation"
    ]
    resources = [
      "*"
    ]
  }
  statement {
    actions = [
      "sqs:SendMessage",
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:GetQueueAttributes",
      "sqs:GetQueueUrl"
    ]
    resources = [
      "arn:aws:sqs:ap-northeast-1:471112919450:store-campaign-update-queue"
    ]
  }

  statement {
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:ListBucket",
      "s3:DeleteObject"
    ]
    resources = [
      "arn:aws:s3:::uat-image/static_resources/store_campaigns/*",
      "arn:aws:s3:::uat-image/private/maps/stores/csv/*",
      "arn:aws:s3:::uat-image/private/maps/special_tags/csv/*",
    ]
  }
  statement {
    actions = [
      "cloudfront:CreateInvalidation"
    ]
    resources = [
      "*"
    ]
  }
}

resource "aws_iam_policy" "admin-map_policy" {
  name        = "admin-map_policy"
  description = "Access to admin-map API Code"
  policy      = data.aws_iam_policy_document.admin-map-policy-doc.json
}
