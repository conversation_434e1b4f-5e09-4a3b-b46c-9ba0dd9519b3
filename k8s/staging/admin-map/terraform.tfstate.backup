{"version": 4, "terraform_version": "1.10.1", "serial": 11, "lineage": "ae7b7864-a7ce-266f-89ff-7c8c7e9b76fe", "outputs": {}, "resources": [{"mode": "data", "type": "aws_iam_policy_document", "name": "admin-map-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "464051715", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": [\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"s3:PutObject\",\n        \"s3:ListBucket\",\n        \"s3:GetObject\",\n        \"s3:DeleteObject\"\n      ],\n      \"Resource\": [\n        \"arn:aws:s3:::uat-image/static_resources/store_campaigns/*\",\n        \"arn:aws:s3:::uat-image\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"cloudfront:CreateInvalidation\",\n      \"Resource\": \"*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"sqs:SendMessage\",\n        \"sqs:ReceiveMessage\",\n        \"sqs:GetQueueUrl\",\n        \"sqs:GetQueueAttributes\",\n        \"sqs:DeleteMessage\"\n      ],\n      \"Resource\": \"arn:aws:sqs:ap-northeast-1:471112919450:store-campaign-update-queue\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"s3:PutObject\",\n        \"s3:ListBucket\",\n        \"s3:GetObject\",\n        \"s3:DeleteObject\"\n      ],\n      \"Resource\": [\n        \"arn:aws:s3:::uat-image/static_resources/store_campaigns/*\",\n        \"arn:aws:s3:::uat-image/private/maps/stores/csv/*\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"cloudfront:CreateInvalidation\",\n      \"Resource\": \"*\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Effect\":\"Allow\",\"Action\":[\"s3:PutObject\",\"s3:ListBucket\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Resource\":[\"arn:aws:s3:::uat-image/static_resources/store_campaigns/*\",\"arn:aws:s3:::uat-image\"]},{\"Effect\":\"Allow\",\"Action\":\"cloudfront:CreateInvalidation\",\"Resource\":\"*\"},{\"Effect\":\"Allow\",\"Action\":[\"sqs:SendMessage\",\"sqs:ReceiveMessage\",\"sqs:GetQueueUrl\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\"],\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:store-campaign-update-queue\"},{\"Effect\":\"Allow\",\"Action\":[\"s3:PutObject\",\"s3:ListBucket\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Resource\":[\"arn:aws:s3:::uat-image/static_resources/store_campaigns/*\",\"arn:aws:s3:::uat-image/private/maps/stores/csv/*\"]},{\"Effect\":\"Allow\",\"Action\":\"cloudfront:CreateInvalidation\",\"Resource\":\"*\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx"], "sid": ""}, {"actions": ["s3:DeleteObject", "s3:GetObject", "s3:ListBucket", "s3:PutObject"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::uat-image", "arn:aws:s3:::uat-image/static_resources/store_campaigns/*"], "sid": ""}, {"actions": ["cloudfront:CreateInvalidation"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}, {"actions": ["sqs:DeleteMessage", "sqs:Get<PERSON>ueueAttributes", "sqs:GetQueueUrl", "sqs:ReceiveMessage", "sqs:SendMessage"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:sqs:ap-northeast-1:471112919450:store-campaign-update-queue"], "sid": ""}, {"actions": ["s3:DeleteObject", "s3:GetObject", "s3:ListBucket", "s3:PutObject"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::uat-image/private/maps/stores/csv/*", "arn:aws:s3:::uat-image/static_resources/store_campaigns/*"], "sid": ""}, {"actions": ["cloudfront:CreateInvalidation"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "admin-map_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/admin-map_policy", "attachment_count": 1, "description": "Access to admin-map API Code", "id": "arn:aws:iam::471112919450:policy/admin-map_policy", "name": "admin-map_policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Action\":[\"s3:PutObject\",\"s3:ListBucket\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::uat-image/static_resources/store_campaigns/*\",\"arn:aws:s3:::uat-image\"]},{\"Action\":\"cloudfront:CreateInvalidation\",\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"sqs:SendMessage\",\"sqs:ReceiveMessage\",\"sqs:GetQueueUrl\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:store-campaign-update-queue\"},{\"Action\":[\"s3:PutObject\",\"s3:ListBucket\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::uat-image/static_resources/store_campaigns/*\",\"arn:aws:s3:::uat-image/private/maps/stores/csv/*\"]},{\"Action\":\"cloudfront:CreateInvalidation\",\"Effect\":\"Allow\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNKUYV4EUCN", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.admin-map-policy-doc"]}]}], "check_results": null}