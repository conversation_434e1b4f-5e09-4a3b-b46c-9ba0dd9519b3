module "iam_eks_role" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"
  version = "5.44.0"

  for_each = local.eks_roles

  role_name = each.value.role_name
  role_policy_arns = each.value.policies

  oidc_providers = {
    eks = {
      provider_arn               = local.eks_oidc_provider_arn
      namespace_service_accounts = ["staging:${each.value.sa_name}"]
    }
  }
}
resource "kubernetes_service_account" "irsa_sa" {
  for_each = local.eks_roles

  metadata {
    name      = each.value.sa_name
    namespace = "staging"
    annotations = {
      "eks.amazonaws.com/role-arn" = module.iam_eks_role[each.key].iam_role_arn
    }
  }
}