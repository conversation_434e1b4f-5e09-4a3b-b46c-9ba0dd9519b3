---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ecfeeder
  namespace: staging
spec:
  selector:
    matchLabels:
      run: ecfeeder
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: ecfeeder
    spec:
      serviceAccountName: ecfeeder-account
      nodeSelector:
        nodegroup: "services"
      tolerations:
        - key: "servicesOnly"
          operator: "Equal"
          value: "true"
          effect: "NoSchedule"
      containers:
        - name: ecfeeder
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/ecfeeder:latest
          imagePullPolicy: Always
          env:
            - name: ENV
              value: staging_eks
#          ports:
#            - containerPort: 8000
          resources:
            limits:
              cpu: 1000m
              memory: 1024Mi
            requests:
              cpu: 1000m
              memory: 1024Mi

#          startupProbe:
#            httpGet:
#              path: /hello/token
#              port: 8000
#              scheme: HTTP
#            failureThreshold: 30
#            periodSeconds: 10
#
#          livenessProbe:
#            httpGet:
#              path: /hello/token
#              port: 8000
#              scheme: HTTP
#            initialDelaySeconds: 30
#            periodSeconds: 10
#            timeoutSeconds: 30
#            successThreshold: 1
#            failureThreshold: 3
#
#          readinessProbe:
#            httpGet:
#              path: /hello/token
#              port: 8000
#              scheme: HTTP
#            initialDelaySeconds: 30
#            timeoutSeconds: 30
#            periodSeconds: 10
#            successThreshold: 1
#            failureThreshold: 3