{"version": 4, "terraform_version": "1.8.4", "serial": 90, "lineage": "0d956dfc-981d-9b92-a4bf-170867f8fbd6", "outputs": {}, "resources": [{"mode": "managed", "type": "aws_eks_node_group", "name": "cronjob_node_group", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"ami_type": "CUSTOM", "arn": "arn:aws:eks:ap-northeast-1:471112919450:nodegroup/uniopen/cronjob-node-group/96cb856e-5bbd-4010-bc28-b67785728298", "capacity_type": "ON_DEMAND", "cluster_name": "uniopen", "disk_size": 0, "force_update_version": null, "id": "uniopen:cronjob-node-group", "instance_types": [], "labels": {"nodegroup": "cronjob"}, "launch_template": [{"id": "lt-0cfaa9799f9f19aaa", "name": "cronjob-20250514091209170800000001", "version": "4"}], "node_group_name": "cronjob-node-group", "node_group_name_prefix": "", "node_repair_config": [], "node_role_arn": "arn:aws:iam::471112919450:role/eks-node-role", "release_version": "ami-0bfea8a210ac80d48", "remote_access": [], "resources": [{"autoscaling_groups": [{"name": "eks-cronjob-node-group-96cb856e-5bbd-4010-bc28-b67785728298"}], "remote_access_security_group_id": ""}], "scaling_config": [{"desired_size": 2, "max_size": 3, "min_size": 1}], "status": "ACTIVE", "subnet_ids": ["subnet-007a652180de503c6", "subnet-0de9a13ff206133e6"], "tags": {"Name": "eks-cronjob-node"}, "tags_all": {"Name": "eks-cronjob-node"}, "taint": [], "timeouts": null, "update_config": [{"max_unavailable": 1, "max_unavailable_percentage": 0}], "version": "1.29"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozNjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInVwZGF0ZSI6MzYwMDAwMDAwMDAwMH19", "dependencies": ["aws_iam_role.eks_node_role", "aws_launch_template.cronjob_lt"]}]}, {"mode": "managed", "type": "aws_eks_node_group", "name": "secrets_store_node_group", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"ami_type": "CUSTOM", "arn": "arn:aws:eks:ap-northeast-1:471112919450:nodegroup/uniopen/secrets-store-group/88cb6b60-d6d5-23f2-80ba-078e6b8d6ca4", "capacity_type": "ON_DEMAND", "cluster_name": "uniopen", "disk_size": 0, "force_update_version": null, "id": "uniopen:secrets-store-group", "instance_types": [], "labels": {"computy-type": "strapi", "nodegroup": "secrets", "secrets-store-only": "true", "type": "strapi"}, "launch_template": [{"id": "lt-00d92dd2cea6ef353", "name": "secret-store-20250516014213186100000001", "version": "3"}], "node_group_name": "secrets-store-group", "node_group_name_prefix": "", "node_repair_config": [], "node_role_arn": "arn:aws:iam::471112919450:role/eks-node-role", "release_version": "ami-0bfea8a210ac80d48", "remote_access": [], "resources": [{"autoscaling_groups": [{"name": "eks-secrets-store-group-88cb6b60-d6d5-23f2-80ba-078e6b8d6ca4"}], "remote_access_security_group_id": ""}], "scaling_config": [{"desired_size": 2, "max_size": 3, "min_size": 1}], "status": "ACTIVE", "subnet_ids": ["subnet-007a652180de503c6", "subnet-0de9a13ff206133e6"], "tags": {"Name": "secrets-store-group"}, "tags_all": {"Name": "secrets-store-group"}, "taint": [], "timeouts": null, "update_config": [{"max_unavailable": 1, "max_unavailable_percentage": 0}], "version": "1.29"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozNjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInVwZGF0ZSI6MzYwMDAwMDAwMDAwMH19", "dependencies": ["aws_iam_role.eks_node_role", "aws_launch_template.secrets_store_launch_template"]}]}, {"mode": "managed", "type": "aws_eks_node_group", "name": "services_node_group", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"ami_type": "CUSTOM", "arn": "arn:aws:eks:ap-northeast-1:471112919450:nodegroup/uniopen/services-node-group/d0cb856e-5bbc-e5d1-b879-6e442b9687ef", "capacity_type": "ON_DEMAND", "cluster_name": "uniopen", "disk_size": 0, "force_update_version": null, "id": "uniopen:services-node-group", "instance_types": [], "labels": {"nodegroup": "services"}, "launch_template": [{"id": "lt-004684b61333c3967", "name": "services-20250515011200273100000001", "version": "5"}], "node_group_name": "services-node-group", "node_group_name_prefix": "", "node_repair_config": [], "node_role_arn": "arn:aws:iam::471112919450:role/eks-node-role", "release_version": "ami-0bfea8a210ac80d48", "remote_access": [], "resources": [{"autoscaling_groups": [{"name": "eks-services-node-group-d0cb856e-5bbc-e5d1-b879-6e442b9687ef"}], "remote_access_security_group_id": ""}], "scaling_config": [{"desired_size": 16, "max_size": 20, "min_size": 10}], "status": "ACTIVE", "subnet_ids": ["subnet-007a652180de503c6", "subnet-0de9a13ff206133e6"], "tags": {"Name": "eks-services-node"}, "tags_all": {"Name": "eks-services-node"}, "taint": [], "timeouts": null, "update_config": [{"max_unavailable": 1, "max_unavailable_percentage": 0}], "version": "1.29"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozNjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInVwZGF0ZSI6MzYwMDAwMDAwMDAwMH19", "dependencies": ["aws_iam_role.eks_node_role", "aws_launch_template.services_lt"]}]}, {"mode": "managed", "type": "aws_iam_role", "name": "eks_node_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:role/eks-node-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ec2.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-05-15T01:16:32Z", "description": "", "force_detach_policies": false, "id": "eks-node-role", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly", "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy", "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy"], "max_session_duration": 3600, "name": "eks-node-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAW3MEEVGNA2LJ6MKWU"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ec2_container_registry_attachment", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "eks-node-role-20250515011633128900000005", "policy_arn": "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly", "role": "eks-node-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_node_role"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "eks_cni_attachment", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "eks-node-role-20250515011633655600000007", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy", "role": "eks-node-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_node_role"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "eks_node_role_attachment", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "eks-node-role-20250515011633393000000006", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy", "role": "eks-node-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_node_role"]}]}, {"mode": "managed", "type": "aws_launch_template", "name": "cronjob_lt", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:471112919450:launch-template/lt-0cfaa9799f9f19aaa", "block_device_mappings": [], "capacity_reservation_specification": [], "cpu_options": [], "credit_specification": [], "default_version": 1, "description": "", "disable_api_stop": false, "disable_api_termination": false, "ebs_optimized": "", "elastic_gpu_specifications": [], "elastic_inference_accelerator": [], "enclave_options": [], "hibernation_options": [], "iam_instance_profile": [], "id": "lt-0cfaa9799f9f19aaa", "image_id": "ami-0bfea8a210ac80d48", "instance_initiated_shutdown_behavior": "", "instance_market_options": [], "instance_requirements": [], "instance_type": "t3.small", "kernel_id": "", "key_name": "", "latest_version": 4, "license_specification": [], "maintenance_options": [], "metadata_options": [{"http_endpoint": "enabled", "http_protocol_ipv6": "", "http_put_response_hop_limit": 2, "http_tokens": "required", "instance_metadata_tags": ""}], "monitoring": [], "name": "cronjob-20250514091209170800000001", "name_prefix": "cronjob-", "network_interfaces": [], "placement": [], "private_dns_name_options": [], "ram_disk_id": "", "security_group_names": [], "tag_specifications": [], "tags": {}, "tags_all": {}, "update_default_version": null, "user_data": "IyEvYmluL2Jhc2gKL2V0Yy9la3MvYm9vdHN0cmFwLnNoIHVuaW9wZW4gXAotLW5vZGUtbGFiZWxzPW5vZGVncm91cD1jcm9uam9iCg==", "vpc_security_group_ids": []}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_launch_template", "name": "secrets_store_launch_template", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:471112919450:launch-template/lt-00d92dd2cea6ef353", "block_device_mappings": [], "capacity_reservation_specification": [], "cpu_options": [], "credit_specification": [], "default_version": 1, "description": "", "disable_api_stop": false, "disable_api_termination": false, "ebs_optimized": "", "elastic_gpu_specifications": [], "elastic_inference_accelerator": [], "enclave_options": [], "hibernation_options": [], "iam_instance_profile": [], "id": "lt-00d92dd2cea6ef353", "image_id": "ami-0bfea8a210ac80d48", "instance_initiated_shutdown_behavior": "", "instance_market_options": [], "instance_requirements": [], "instance_type": "t3.medium", "kernel_id": "", "key_name": "", "latest_version": 3, "license_specification": [], "maintenance_options": [], "metadata_options": [{"http_endpoint": "enabled", "http_protocol_ipv6": "", "http_put_response_hop_limit": 2, "http_tokens": "required", "instance_metadata_tags": "enabled"}], "monitoring": [], "name": "secret-store-20250516014213186100000001", "name_prefix": "secret-store-", "network_interfaces": [], "placement": [], "private_dns_name_options": [], "ram_disk_id": "", "security_group_names": [], "tag_specifications": [{"resource_type": "instance", "tags": {"Name": "secret-store-node"}}], "tags": {}, "tags_all": {}, "update_default_version": null, "user_data": "IyEvYmluL2Jhc2gKL2V0Yy9la3MvYm9vdHN0cmFwLnNoIHVuaW9wZW4K", "vpc_security_group_ids": []}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_launch_template", "name": "services_lt", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:471112919450:launch-template/lt-004684b61333c3967", "block_device_mappings": [], "capacity_reservation_specification": [], "cpu_options": [], "credit_specification": [], "default_version": 1, "description": "", "disable_api_stop": false, "disable_api_termination": false, "ebs_optimized": "", "elastic_gpu_specifications": [], "elastic_inference_accelerator": [], "enclave_options": [], "hibernation_options": [], "iam_instance_profile": [], "id": "lt-004684b61333c3967", "image_id": "ami-0bfea8a210ac80d48", "instance_initiated_shutdown_behavior": "", "instance_market_options": [], "instance_requirements": [], "instance_type": "t3.small", "kernel_id": "", "key_name": "", "latest_version": 5, "license_specification": [], "maintenance_options": [], "metadata_options": [{"http_endpoint": "enabled", "http_protocol_ipv6": "", "http_put_response_hop_limit": 2, "http_tokens": "required", "instance_metadata_tags": ""}], "monitoring": [], "name": "services-20250515011200273100000001", "name_prefix": "services-", "network_interfaces": [], "placement": [], "private_dns_name_options": [], "ram_disk_id": "", "security_group_names": [], "tag_specifications": [], "tags": {}, "tags_all": {}, "update_default_version": null, "user_data": "IyEvYmluL2Jhc2gKL2V0Yy9la3MvYm9vdHN0cmFwLnNoIHVuaW9wZW4gXAotLW5vZGUtbGFiZWxzPW5vZGVncm91cD1zZXJ2aWNlcwo=", "vpc_security_group_ids": []}, "sensitive_attributes": [], "private": "bnVsbA=="}]}], "check_results": null}