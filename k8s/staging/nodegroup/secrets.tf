resource "aws_eks_node_group" "secrets_store_node_group" {
  cluster_name    = local.cluster_name
  node_group_name = "secrets-store-group"
  node_role_arn   = aws_iam_role.eks_node_role.arn
  subnet_ids      = local.subnet_ids

  launch_template {
    id      = aws_launch_template.secrets_store_launch_template.id
    version = "$Latest"
  }

  scaling_config {
    desired_size = 2
    max_size     = 3
    min_size     = 1
  }

  labels = {
    "nodegroup" = "secrets"
  }

  tags = {
    "Name" = "secrets-store-group"
  }
}
resource "aws_launch_template" "secrets_store_launch_template" {
  name_prefix   = "secret-store-"
  image_id      = local.ami
  instance_type = "t3.medium"

  user_data = base64encode(<<-EOT
    #!/bin/bash
    /etc/eks/bootstrap.sh ${local.cluster_name}
  EOT
  )

  metadata_options {
    http_endpoint               = "enabled"
    http_tokens                 = "required"   # ✅ 強制 IMDSv2
    http_put_response_hop_limit = 2
  }

  tag_specifications {
    resource_type = "instance"
    tags = {
      Name = "secret-store-node"
    }
  }
}

