resource "aws_eks_node_group" "cronjob_node_group" {
  cluster_name    = local.cluster_name
  node_group_name = "cronjob-node-group"
  node_role_arn   = aws_iam_role.eks_node_role.arn
  subnet_ids      = local.subnet_ids

  scaling_config {
    desired_size = 2
    max_size     = 3
    min_size     = 1
  }

  launch_template {
    id      = aws_launch_template.cronjob_lt.id
    version = "$Latest"
  }

  labels = {
    nodegroup = "cronjob"
  }

  tags = {
    Name = "eks-cronjob-node"
  }
}

resource "aws_launch_template" "cronjob_lt" {
  name_prefix   = "cronjob-"
  image_id      = local.ami
  instance_type = local.instance_type

  metadata_options {
    http_endpoint               = "enabled"
    http_tokens                 = "required"   # ✅ 強制 IMDSv2
    http_put_response_hop_limit = 2
  }

  user_data = base64encode(<<-EOT
              #!/bin/bash
              /etc/eks/bootstrap.sh ${local.cluster_name} \
              --node-labels=nodegroup=cronjob
            EOT
  )
}
