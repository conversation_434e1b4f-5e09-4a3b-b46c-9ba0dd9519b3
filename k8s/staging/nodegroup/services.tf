resource "aws_eks_node_group" "services_node_group" {
  cluster_name    = local.cluster_name
  node_group_name = "services-node-group"
  node_role_arn   = aws_iam_role.eks_node_role.arn
  subnet_ids      = local.subnet_ids

  launch_template {
    id      = aws_launch_template.services_lt.id
    version = "$Latest"
  }

  scaling_config {
    desired_size = 16
    max_size     = 20
    min_size     = 10
  }

  labels = {
    nodegroup = "services"
  }

  tags = {
    Name = "eks-services-node"
  }
}

resource "aws_launch_template" "services_lt" {
  name_prefix   = "services-"
  image_id      = local.ami
  instance_type = local.instance_type

  metadata_options {
    http_endpoint               = "enabled"
    http_tokens                 = "required"   # ✅ 強制 IMDSv2
    http_put_response_hop_limit = 2
  }

  user_data = base64encode(<<-EOT
              #!/bin/bash
              /etc/eks/bootstrap.sh ${local.cluster_name} \
              --node-labels=nodegroup=services
            EOT
  )
}
