provider "aws" {
  region = "ap-northeast-1"
}
data "aws_iam_policy_document" "user-policy-doc" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx"
    ]
  }

  statement {
    actions = [
      "sqs:SendMessage",
      "sqs:GetQueueUrl",
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:GetQueueAttributes",
    ]
    resources = [
      "arn:aws:sqs:ap-northeast-1:471112919450:profile-updater-queue"
    ]
  }

  statement {
    actions = [
      "sqs:SendMessage",
      "sqs:ReceiveMessage",
      "sqs:GetQueueUrl",
      "sqs:GetQueueAttributes",
    ]
    resources = [
      "arn:aws:sqs:ap-northeast-1:471112919450:member-campaign-event-queue"
    ]
  }
}

resource "aws_iam_policy" "user_policy" {
  name        = "user_policy"
  description = "Access to user API Code"
  policy      = data.aws_iam_policy_document.user-policy-doc.json
}