{"version": 4, "terraform_version": "1.8.4", "serial": 5, "lineage": "d59e8886-51d4-6bde-6ed6-33a8dc8549c2", "outputs": {}, "resources": [{"mode": "data", "type": "aws_iam_policy_document", "name": "user-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": [\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"sqs:SendMessage\",\n        \"sqs:ReceiveMessage\",\n        \"sqs:GetQueueUrl\",\n        \"sqs:GetQueueAttributes\",\n        \"sqs:DeleteMessage\"\n      ],\n      \"Resource\": \"arn:aws:sqs:ap-northeast-1:471112919450:profile-updater-queue\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Effect\":\"Allow\",\"Action\":[\"sqs:SendMessage\",\"sqs:ReceiveMessage\",\"sqs:GetQueueUrl\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\"],\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:profile-updater-queue\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx"], "sid": ""}, {"actions": ["sqs:DeleteMessage", "sqs:Get<PERSON>ueueAttributes", "sqs:GetQueueUrl", "sqs:ReceiveMessage", "sqs:SendMessage"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:sqs:ap-northeast-1:471112919450:profile-updater-queue"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "user_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/user_policy", "attachment_count": 1, "description": "Access to user API Code", "id": "arn:aws:iam::471112919450:policy/user_policy", "name": "user_policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Action\":[\"sqs:SendMessage\",\"sqs:ReceiveMessage\",\"sqs:GetQueueUrl\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:profile-updater-queue\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNCXM7VMOCN", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.user-policy-doc"]}]}], "check_results": null}