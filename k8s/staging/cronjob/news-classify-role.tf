data "aws_iam_policy_document" "cronjob-news-classify-policy-doc" {
 statement {
    actions = [
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:GetQueueAttributes",
    ]
    resources = [
      "arn:aws:sqs:ap-northeast-1:471112919450:news-classification-queue"
    ]
  }
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret",
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F"
    ]
  }
}

resource "aws_iam_policy" "cronjob-news-classify-policy" {
  name        = "cronjob-news-classify-policy"
  description = "Access secret-manager / bedrock"
  policy      = data.aws_iam_policy_document.cronjob-news-classify-policy-doc.json
}