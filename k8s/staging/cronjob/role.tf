data "aws_iam_policy_document" "cronjob-banner-generator-policy-doc" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0"
    ]
  }
  statement {
    actions = [
      "cloudfront:CreateInvalidation"
    ]
    resources = [
      "*",
    ]
  }
  statement {
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:PutObjectAcl",
      "s3:GetObjectAcl",
      "s3:DeleteObject",
    ]
    resources = [
      "arn:aws:s3:::uat-image/*",
    ]
  }
  statement {
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      "arn:aws:s3:::uat-image",
    ]
  }
}

resource "aws_iam_policy" "cronjob-banner-generator-policy" {
  name        = "cronjob-banner-generator-policy"
  description = "Access docdb / s3 / cloudfront"
  policy      = data.aws_iam_policy_document.cronjob-banner-generator-policy-doc.json
}