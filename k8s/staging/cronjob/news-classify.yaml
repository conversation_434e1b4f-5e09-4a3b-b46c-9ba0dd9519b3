apiVersion: batch/v1
kind: CronJob
metadata:
  name: news-classify-cronjob
  namespace: staging
spec:
  schedule: "*/10 0-12 * * 1-5"
  suspend: true
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: news-classify-cronjob
        spec:
          serviceAccountName: news-classify-account
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          containers:
          - name: cronjob-news-classify
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/cronjob-news-classify:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1