data "aws_iam_policy_document" "cronjob-popularity-updater-policy-doc" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret",
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F"
    ]
  }
}

resource "aws_iam_policy" "cronjob-popularity-updater-policy" {
  name        = "cronjob-popularity-updater-policy"
  description = ""
  policy      = data.aws_iam_policy_document.cronjob-popularity-updater-policy-doc.json
}
