apiVersion: batch/v1
kind: CronJob
metadata:
  name: ig-crawler-cronjob
  namespace: staging
spec:
  schedule: "0 1,7 * * 1-5"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: ig-crawler-cronjob
        spec:
          serviceAccountName: ig-crawler-account
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          containers:
          - name: ig-crawler-cronjob
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/cronjob-ig-crawler:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1