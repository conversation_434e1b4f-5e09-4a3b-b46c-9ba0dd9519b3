{"version": 4, "terraform_version": "1.10.4", "serial": 58, "lineage": "ba1e0cce-16e1-da57-d55c-e83df6667140", "outputs": {}, "resources": [{"mode": "data", "type": "aws_iam_policy_document", "name": "cronjob-banner-generator-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": [\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"cloudfront:CreateInvalidation\",\n      \"Resource\": \"*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"s3:PutObjectAcl\",\n        \"s3:PutObject\",\n        \"s3:GetObjectAcl\",\n        \"s3:GetObject\",\n        \"s3:DeleteObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::uat-image/*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::uat-image\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Effect\":\"Allow\",\"Action\":\"cloudfront:CreateInvalidation\",\"Resource\":\"*\"},{\"Effect\":\"Allow\",\"Action\":[\"s3:PutObjectAcl\",\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Resource\":\"arn:aws:s3:::uat-image/*\"},{\"Effect\":\"Allow\",\"Action\":\"s3:ListBucket\",\"Resource\":\"arn:aws:s3:::uat-image\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0"], "sid": ""}, {"actions": ["cloudfront:CreateInvalidation"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}, {"actions": ["s3:DeleteObject", "s3:GetObject", "s3:GetObjectAcl", "s3:PutObject", "s3:PutObjectAcl"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::uat-image/*"], "sid": ""}, {"actions": ["s3:ListBucket"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::uat-image"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "cronjob-campaign-report-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": [\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n      ]\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "cronjob-counter-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"sqs:ReceiveMessage\",\n        \"sqs:GetQueueUrl\",\n        \"sqs:GetQueueAttributes\",\n        \"sqs:DeleteMessage\"\n      ],\n      \"Resource\": \"arn:aws:sqs:ap-northeast-1:471112919450:counter-queue\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"sqs:ReceiveMessage\",\"sqs:GetQueueUrl\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\"],\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:counter-queue\"},{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["sqs:DeleteMessage", "sqs:Get<PERSON>ueueAttributes", "sqs:GetQueueUrl", "sqs:ReceiveMessage"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:sqs:ap-northeast-1:471112919450:counter-queue"], "sid": ""}, {"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "cronjob-ec-generator-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"cloudfront:CreateInvalidation\",\n      \"Resource\": \"*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"s3:PutObjectAcl\",\n        \"s3:PutObject\",\n        \"s3:GetObjectAcl\",\n        \"s3:GetObject\",\n        \"s3:DeleteObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::uat-image/*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"appconfig:StartConfigurationSession\",\n        \"appconfig:GetLatestConfiguration\"\n      ],\n      \"Resource\": [\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"\n      ]\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":\"cloudfront:CreateInvalidation\",\"Resource\":\"*\"},{\"Effect\":\"Allow\",\"Action\":[\"s3:PutObjectAcl\",\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Resource\":\"arn:aws:s3:::uat-image/*\"},{\"Effect\":\"Allow\",\"Action\":[\"appconfig:StartConfigurationSession\",\"appconfig:GetLatestConfiguration\"],\"Resource\":[\"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"]}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["cloudfront:CreateInvalidation"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}, {"actions": ["s3:DeleteObject", "s3:GetObject", "s3:GetObjectAcl", "s3:PutObject", "s3:PutObjectAcl"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::uat-image/*"], "sid": ""}, {"actions": ["appconfig:GetLatestConfiguration", "appconfig:StartConfigurationSession"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:appconfig:ap-northeast-1:471112919450:application/*", "arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*", "arn:aws:appconfig:ap-northeast-1:471112919450:environment/*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "cronjob-ecr-security-reporter-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "202834469", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": [\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:jira-user-email-9gMtXY\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:jira-api-token-ceqCpV\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"inspector:ListFindings\",\n        \"inspector:DescribeFindings\",\n        \"inspector2:ListFindings\",\n        \"inspector2:ListFindingAggregations\",\n        \"inspector2:ListCoverageStatistics\",\n        \"inspector2:ListCoverage\",\n        \"inspector2:GetFindingsReportStatus\",\n        \"ecr:ListTagsForResource\",\n        \"ecr:DescribeRepositories\",\n        \"ecr:DescribeImages\",\n        \"ecr:DescribeImageScanFindings\"\n      ],\n      \"Resource\": \"*\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:jira-user-email-9gMtXY\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:jira-api-token-ceqCpV\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Effect\":\"Allow\",\"Action\":[\"inspector:ListFindings\",\"inspector:DescribeFindings\",\"inspector2:ListFindings\",\"inspector2:ListFindingAggregations\",\"inspector2:ListCoverageStatistics\",\"inspector2:ListCoverage\",\"inspector2:GetFindingsReportStatus\",\"ecr:ListTagsForResource\",\"ecr:DescribeRepositories\",\"ecr:DescribeImages\",\"ecr:DescribeImageScanFindings\"],\"Resource\":\"*\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:jira-api-token-ceqCpV", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:jira-user-email-9gMtXY"], "sid": ""}, {"actions": ["ecr:DescribeImageScanFindings", "ecr:DescribeImages", "ecr:DescribeRepositories", "ecr:ListTagsForResource", "inspector2:GetFindingsReportStatus", "inspector2:ListCoverage", "inspector2:ListCoverageStatistics", "inspector2:ListFindingAggregations", "inspector2:<PERSON><PERSON><PERSON><PERSON>", "inspector:Des<PERSON><PERSON><PERSON><PERSON>", "inspector:<PERSON><PERSON><PERSON><PERSON>"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "cronjob-ig-crawler-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": [\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:instagram-user-access-token-AFfTkv\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"cloudfront:CreateInvalidation\",\n      \"Resource\": \"*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"s3:PutObjectAcl\",\n        \"s3:PutObject\",\n        \"s3:GetObjectAcl\",\n        \"s3:GetObject\",\n        \"s3:DeleteObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::uat-image/*\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:instagram-user-access-token-AFfTkv\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Effect\":\"Allow\",\"Action\":\"cloudfront:CreateInvalidation\",\"Resource\":\"*\"},{\"Effect\":\"Allow\",\"Action\":[\"s3:PutObjectAcl\",\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Resource\":\"arn:aws:s3:::uat-image/*\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:instagram-user-access-token-AFfTkv"], "sid": ""}, {"actions": ["cloudfront:CreateInvalidation"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}, {"actions": ["s3:DeleteObject", "s3:GetObject", "s3:GetObjectAcl", "s3:PutObject", "s3:PutObjectAcl"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::uat-image/*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "cronjob-mgm-ranking-generator-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"cloudfront:CreateInvalidation\",\n      \"Resource\": \"*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"s3:PutObjectAcl\",\n        \"s3:PutObject\",\n        \"s3:GetObjectAcl\",\n        \"s3:GetObject\",\n        \"s3:DeleteObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::uat-image/*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::uat-image\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"},{\"Effect\":\"Allow\",\"Action\":\"cloudfront:CreateInvalidation\",\"Resource\":\"*\"},{\"Effect\":\"Allow\",\"Action\":[\"s3:PutObjectAcl\",\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Resource\":\"arn:aws:s3:::uat-image/*\"},{\"Effect\":\"Allow\",\"Action\":\"s3:ListBucket\",\"Resource\":\"arn:aws:s3:::uat-image\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F"], "sid": ""}, {"actions": ["cloudfront:CreateInvalidation"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}, {"actions": ["s3:DeleteObject", "s3:GetObject", "s3:GetObjectAcl", "s3:PutObject", "s3:PutObjectAcl"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::uat-image/*"], "sid": ""}, {"actions": ["s3:ListBucket"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::uat-image"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "cronjob-news-classify-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "210403900", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"sqs:ReceiveMessage\",\n        \"sqs:GetQueueAttributes\",\n        \"sqs:DeleteMessage\"\n      ],\n      \"Resource\": \"arn:aws:sqs:ap-northeast-1:471112919450:news-classification-queue\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"sqs:ReceiveMessage\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\"],\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:news-classification-queue\"},{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["sqs:DeleteMessage", "sqs:Get<PERSON>ueueAttributes", "sqs:ReceiveMessage"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:sqs:ap-northeast-1:471112919450:news-classification-queue"], "sid": ""}, {"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "cronjob-news-feeder-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": [\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging_news_sha_key-X9WUBi\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-secret-kHy1tN\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-id-RwqoSI\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"sqs:SendMessage\",\n        \"sqs:GetQueueUrl\"\n      ],\n      \"Resource\": \"arn:aws:sqs:ap-northeast-1:471112919450:news-classification-queue\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging_news_sha_key-X9WUBi\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-secret-kHy1tN\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-id-RwqoSI\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Effect\":\"Allow\",\"Action\":[\"sqs:SendMessage\",\"sqs:GetQueueUrl\"],\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:news-classification-queue\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-id-RwqoSI", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-secret-kHy1tN", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging_news_sha_key-X9WUBi"], "sid": ""}, {"actions": ["sqs:GetQueueUrl", "sqs:SendMessage"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:sqs:ap-northeast-1:471112919450:news-classification-queue"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "cronjob-popularity-updater-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "cronjob-profile-updater-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"sqs:ReceiveMessage\",\n        \"sqs:GetQueueUrl\",\n        \"sqs:GetQueueAttributes\",\n        \"sqs:DeleteMessage\"\n      ],\n      \"Resource\": \"arn:aws:sqs:ap-northeast-1:471112919450:profile-updater-queue\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"sqs:ReceiveMessage\",\"sqs:GetQueueUrl\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\"],\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:profile-updater-queue\"},{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["sqs:DeleteMessage", "sqs:Get<PERSON>ueueAttributes", "sqs:GetQueueUrl", "sqs:ReceiveMessage"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:sqs:ap-northeast-1:471112919450:profile-updater-queue"], "sid": ""}, {"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "cronjob-store-product-fetch-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "262672369", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": [\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-seven-eleven-sftp-pwd-STK0dW\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n      ]\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-seven-eleven-sftp-pwd-STK0dW\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-seven-eleven-sftp-pwd-STK0dW"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "cronjob-banner-generator-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/cronjob-banner-generator-policy", "attachment_count": 1, "description": "Access docdb / s3 / cloudfront", "id": "arn:aws:iam::471112919450:policy/cronjob-banner-generator-policy", "name": "cronjob-banner-generator-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Action\":\"cloudfront:CreateInvalidation\",\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"s3:PutObjectAcl\",\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:s3:::uat-image/*\"},{\"Action\":\"s3:ListBucket\",\"Effect\":\"Allow\",\"Resource\":\"arn:aws:s3:::uat-image\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNEIIEO26O2", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.cronjob-banner-generator-policy-doc"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "cronjob-campaign-report-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/cronjob-campaign-report-policy", "attachment_count": 1, "description": "Access secret-manager / document_db", "id": "arn:aws:iam::471112919450:policy/cronjob-campaign-report-policy", "name": "cronjob-campaign-report-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNLZ2AA6CTQ", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.cronjob-campaign-report-policy-doc"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "cronjob-counter-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/cronjob-counter-policy", "attachment_count": 1, "description": "Access secret-manager / counter-queue / document_db", "id": "arn:aws:iam::471112919450:policy/cronjob-counter-policy", "name": "cronjob-counter-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"sqs:ReceiveMessage\",\"sqs:GetQueueUrl\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:counter-queue\"},{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNKEHLS6EV5", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.cronjob-counter-policy-doc"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "cronjob-ec-generator-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/cronjob-ec-generator-policy", "attachment_count": 1, "description": "Access s3 / cloudfront", "id": "arn:aws:iam::471112919450:policy/cronjob-ec-generator-policy", "name": "cronjob-ec-generator-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":\"cloudfront:CreateInvalidation\",\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"s3:PutObjectAcl\",\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:s3:::uat-image/*\"},{\"Action\":[\"appconfig:StartConfigurationSession\",\"appconfig:GetLatestConfiguration\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"]}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNCEMH4IMAW", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.cronjob-ec-generator-policy-doc"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "cronjob-ecr-security-reporter-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/cronjob-ecr-security-reporter-policy", "attachment_count": 1, "description": "Access secret-manager / ECR", "id": "arn:aws:iam::471112919450:policy/cronjob-ecr-security-reporter-policy", "name": "cronjob-ecr-security-reporter-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:jira-user-email-9gMtXY\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:jira-api-token-ceqCpV\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Action\":[\"inspector:ListFindings\",\"inspector:DescribeFindings\",\"inspector2:ListFindings\",\"inspector2:ListFindingAggregations\",\"inspector2:ListCoverageStatistics\",\"inspector2:ListCoverage\",\"inspector2:GetFindingsReportStatus\",\"ecr:ListTagsForResource\",\"ecr:DescribeRepositories\",\"ecr:DescribeImages\",\"ecr:DescribeImageScanFindings\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNHRUYAQ5TF", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.cronjob-ecr-security-reporter-doc"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "cronjob-ig-crawler-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/cronjob-ig-crawler-policy", "attachment_count": 1, "description": "Access secret-manager / s3 / cloudfront", "id": "arn:aws:iam::471112919450:policy/cronjob-ig-crawler-policy", "name": "cronjob-ig-crawler-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:instagram-user-access-token-AFfTkv\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Action\":\"cloudfront:CreateInvalidation\",\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"s3:PutObjectAcl\",\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:s3:::uat-image/*\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNLUM3DPPX7", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.cronjob-ig-crawler-policy-doc"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "cronjob-mgm-ranking-generator-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/cronjob-mgm-ranking-generator-policy", "attachment_count": 1, "description": "Access secret-manager / bedrock", "id": "arn:aws:iam::471112919450:policy/cronjob-mgm-ranking-generator-policy", "name": "cronjob-mgm-ranking-generator-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"},{\"Action\":\"cloudfront:CreateInvalidation\",\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"s3:PutObjectAcl\",\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:s3:::uat-image/*\"},{\"Action\":\"s3:ListBucket\",\"Effect\":\"Allow\",\"Resource\":\"arn:aws:s3:::uat-image\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNKJGETMRIZ", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.cronjob-mgm-ranking-generator-policy-doc"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "cronjob-news-classify-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/cronjob-news-classify-policy", "attachment_count": 1, "description": "Access secret-manager / bedrock", "id": "arn:aws:iam::471112919450:policy/cronjob-news-classify-policy", "name": "cronjob-news-classify-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"sqs:ReceiveMessage\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:news-classification-queue\"},{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNICJX6V6YN", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.cronjob-news-classify-policy-doc"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "cronjob-news-feeder-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/cronjob-news-feeder-policy", "attachment_count": 1, "description": "Access secret-manager / bedrock", "id": "arn:aws:iam::471112919450:policy/cronjob-news-feeder-policy", "name": "cronjob-news-feeder-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging_news_sha_key-X9WUBi\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-secret-kHy1tN\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-id-RwqoSI\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Action\":[\"sqs:SendMessage\",\"sqs:GetQueueUrl\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:news-classification-queue\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNPOE3VGMHJ", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.cronjob-news-feeder-policy-doc"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "cronjob-popularity-updater-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/cronjob-popularity-updater-policy", "attachment_count": 1, "description": "", "id": "arn:aws:iam::471112919450:policy/cronjob-popularity-updater-policy", "name": "cronjob-popularity-updater-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNDUQBLWF5G", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.cronjob-popularity-updater-policy-doc"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "cronjob-profile-updater-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/cronjob-profile-updater-policy", "attachment_count": 1, "description": "Access secret-manager / profile-updater-queue / document_db", "id": "arn:aws:iam::471112919450:policy/cronjob-profile-updater-policy", "name": "cronjob-profile-updater-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"sqs:ReceiveMessage\",\"sqs:GetQueueUrl\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:sqs:ap-northeast-1:471112919450:profile-updater-queue\"},{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNHAPRTWNUO", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.cronjob-profile-updater-policy-doc"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "cronjob-store-product-fetch-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/cronjob-store-product-fetch-policy", "attachment_count": 1, "description": "Access secret-manager / document_db", "id": "arn:aws:iam::471112919450:policy/cronjob-store-product-fetch-policy", "name": "cronjob-store-product-fetch-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-seven-eleven-sftp-pwd-STK0dW\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGND5GDOPA7Y", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.cronjob-store-product-fetch-policy-doc"]}]}], "check_results": null}