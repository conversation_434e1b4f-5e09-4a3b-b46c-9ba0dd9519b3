apiVersion: batch/v1
kind: CronJob
metadata:
  name: cronjob-status-changer
  namespace: staging
spec:
  schedule: "12 * * * 1-5"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: cronjob-status-changer
        spec:
          serviceAccountName: status-changer-account
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          containers:
          - name: cronjob-status-changer
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/cronjob-status-changer:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1