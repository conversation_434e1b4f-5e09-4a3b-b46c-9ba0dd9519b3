apiVersion: batch/v1
kind: CronJob
metadata:
  name: cronjob-popularity-updater
  namespace: staging
spec:
  schedule: "*/5 0-12 * * 1-5"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: cronjob-popularity-updater
        spec:
          serviceAccountName: popularity-updater-account
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          containers:
            - name: cronjob-popularity-updater
              image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/cronjob-popularity-updater:latest
              imagePullPolicy: Always
              env:
                - name: ENV
                  value: "staging"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
