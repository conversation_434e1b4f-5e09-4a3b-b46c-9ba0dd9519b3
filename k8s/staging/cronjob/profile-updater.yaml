apiVersion: batch/v1
kind: CronJob
metadata:
  name: profile-updater-cronjob
  namespace: staging
spec:
  schedule: "*/5 0-12 * * 1-5"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: profile-updater-cronjob
        spec:
          serviceAccountName: profile-updater-account
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          containers:
          - name: cronjob-profile-updater
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/cronjob-profile-updater:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1