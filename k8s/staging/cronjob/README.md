# Add service account

## ig-crawler
```
eksctl create iamserviceaccount --namespace staging --cluster uniopen --region ap-northeast-1 --approve --profile staging --override-existing-serviceaccounts \
    --name ig-crawler-account --role-name cronjob-ig-crawler-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/cronjob-ig-crawler-policy
```
## news-classify
```
eksctl create iamserviceaccount --namespace staging --cluster uniopen --region ap-northeast-1 --approve --profile staging --override-existing-serviceaccounts \
    --name news-classify-account --role-name cronjob-news-classify-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/cronjob-news-classify-policy
```

## counter
```
eksctl create iamserviceaccount --namespace staging --cluster uniopen --region ap-northeast-1 --approve --profile staging --override-existing-serviceaccounts \
    --name counter-account --role-name cronjob-counter-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/cronjob-counter-policy
```

## news-feeder
```
eksctl create iamserviceaccount --namespace staging --cluster uniopen --region ap-northeast-1 --approve --profile staging --override-existing-serviceaccounts \
    --name news-feeder-account --role-name cronjob-news-feeder-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/cronjob-news-feeder-policy
```

## banner-generator
```
eksctl create iamserviceaccount --name banner-generator-account --namespace staging --cluster uniopen --role-name cronjob-banner-generator-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/cronjob-banner-generator-policy --region ap-northeast-1 --approve --profile staging --override-existing-serviceaccounts 
```

## campaign-report
```
eksctl create iamserviceaccount --name campaign-report-account --namespace staging --cluster uniopen --role-name cronjob-campaign-report-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/cronjob-campaign-report-policy --region ap-northeast-1 --approve --profile staging --override-existing-serviceaccounts 

```
## store-product-fetch
```
eksctl create iamserviceaccount --name store-product-fetch-account --namespace staging --cluster uniopen --role-name cronjob-store-product-fetch-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/cronjob-store-product-fetch-policy --region ap-northeast-1 --approve --profile staging --override-existing-serviceaccounts 
```

## ecr-security-reporter
```
eksctl create iamserviceaccount --namespace staging --cluster uniopen --region ap-northeast-1 --approve --profile staging --override-existing-serviceaccounts \
    --name ecr-security-reporter-account --role-name cronjob-ecr-security-reporter-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/cronjob-ecr-security-reporter-policy
```

## profile-updater
```
eksctl create iamserviceaccount --namespace staging --cluster uniopen --region ap-northeast-1 --approve --profile staging --override-existing-serviceaccounts \
    --name profile-updater-account --role-name cronjob-profile-updater-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/cronjob-profile-updater-policy
```

## popularity-updater
```
eksctl create iamserviceaccount --namespace staging --cluster uniopen --region ap-northeast-1 --approve --profile staging --override-existing-serviceaccounts \
    --name popularity-updater-account --role-name cronjob-popularity-updater-assume-role \
    --attach-policy-arn arn:aws:iam::************:policy/cronjob-popularity-updater-policy
```

# How to deploy a cronjob service

Steps:
1. Create and apply iam policy with terraform
2. Add service account with eksctl. Example: [Popularity Updater](#popularity-updater)
3. Apply k8s with kubectl. ```kubectl apply -f {k8s_yaml_file}```