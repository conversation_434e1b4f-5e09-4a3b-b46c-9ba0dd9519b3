apiVersion: batch/v1
kind: CronJob
metadata:
  name: article-search-indexer-cronjob-full
  namespace: staging
spec:
  schedule: "0 0 * * 1-5"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: article-search-indexer-cronjob
        spec:
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          serviceAccountName: banner-generator-account
          containers:
          - name: article-search-indexer-cronjob
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/article-search-indexer:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
              - name: ACTION_TYPE
                value: "FULL"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: article-search-indexer-cronjob-incremental
  namespace: staging
spec:
  schedule: "*/30 0-11 * * 1-5"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: article-search-indexer-cronjob
        spec:
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          serviceAccountName: banner-generator-account
          containers:
          - name: article-search-indexer-cronjob
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/article-search-indexer:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
              - name: ACTION_TYPE
                value: "UPDATE"
              - name: MIN_BEFORE
                value: "60"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1