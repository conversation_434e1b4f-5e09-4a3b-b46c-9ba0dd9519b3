apiVersion: batch/v1
kind: CronJob
metadata:
  name: banner-feeder-cronjob
  namespace: staging
spec:
  schedule: "5 15 * * 1-5"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: banner-feeder-cronjob
        spec:
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          serviceAccountName: media-account
          containers:
          - name: banner-feeder-cronjob
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/banner-feeder:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
              - name: BU_NAME
                value: "BOOKS"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1