apiVersion: batch/v1
kind: CronJob
metadata:
  name: ecr-security-reporter-cronjob
  namespace: staging
spec:
  schedule: "0 0 * * 1-5"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: ecr-security-reporter-cronjob
        spec:
          serviceAccountName: ecr-security-reporter-account
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          containers:
          - name: cronjob-ecr-security-reporter
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/cronjob-ecr-security-reporter:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1