apiVersion: batch/v1
kind: CronJob
metadata:
  name: campaign-report-cronjob
  namespace: staging
spec:
  schedule: "9 0-12 * * 1-5"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: campaign-report-cronjob
        spec:
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          serviceAccountName: campaign-report-account
          containers:
          - name: campaign-report-cronjob
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/cronjob-campaign-report:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1