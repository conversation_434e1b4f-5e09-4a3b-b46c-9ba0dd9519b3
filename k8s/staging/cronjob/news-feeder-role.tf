data "aws_iam_policy_document" "cronjob-news-feeder-policy-doc" {
 statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging_news_sha_key-X9WUBi",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-image-hmac-sha256-key-5MRHF0",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-id-RwqoSI",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-books-client-secret-kHy1tN"
    ]
  }
  statement {
    actions = [
      "sqs:SendMessage",
      "sqs:GetQueueUrl"
    ]
    resources = [
      "arn:aws:sqs:ap-northeast-1:471112919450:news-classification-queue",
    ]
  }
}

resource "aws_iam_policy" "cronjob-news-feeder-policy" {
  name        = "cronjob-news-feeder-policy"
  description = "Access secret-manager / bedrock"
  policy      = data.aws_iam_policy_document.cronjob-news-feeder-policy-doc.json
}