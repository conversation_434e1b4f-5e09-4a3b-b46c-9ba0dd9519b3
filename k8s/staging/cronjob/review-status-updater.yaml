apiVersion: batch/v1
kind: CronJob
metadata:
  name: review-status-updater-cronjob
  namespace: staging
spec:
  schedule: "11,22,33,44,55 0-12 * * 1-5"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: review-status-updater-cronjob
        spec:
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          containers:
          - name: cronjob-review-status-updater
            image: 471112919450.dkr.ecr.ap-northeast-1.amazonaws.com/cronjob-review-status-updater:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1