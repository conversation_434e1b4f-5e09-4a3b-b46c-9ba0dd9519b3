data "aws_iam_policy_document" "cronjob-ec-generator-policy-doc" {
  statement {
    actions = [
      "cloudfront:CreateInvalidation"
    ]
    resources = [
      "*",
    ]
  }
  statement {
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:PutObjectAcl",
      "s3:GetObjectAcl",
      "s3:DeleteObject"
    ]
    resources = [
      "arn:aws:s3:::uat-image/*",
    ]
  }
  statement {
    effect = "Allow"
    actions = [
      "appconfig:StartConfigurationSession",
      "appconfig:GetLatestConfiguration"
    ]
    resources = [
        "arn:aws:appconfig:ap-northeast-1:471112919450:application/*",
        "arn:aws:appconfig:ap-northeast-1:471112919450:environment/*",
        "arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*"
      ]
  }
}

resource "aws_iam_policy" "cronjob-ec-generator-policy" {
  name        = "cronjob-ec-generator-policy"
  description = "Access s3 / cloudfront"
  policy      = data.aws_iam_policy_document.cronjob-ec-generator-policy-doc.json
}