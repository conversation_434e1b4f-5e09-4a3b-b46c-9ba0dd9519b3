data "aws_iam_policy_document" "cronjob-ecr-security-reporter-doc" {
 statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:jira-user-email-9gMtXY",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:jira-api-token-ceqCpV"
    ]
  }
  statement {
    actions = [
      "ecr:DescribeRepositories",
      "ecr:ListTagsForResource",
      "ecr:DescribeImageScanFindings",
      "ecr:DescribeImages",
      "inspector2:ListCoverageStatistics",
      "inspector2:ListFindings",
      "inspector2:ListFindingAggregations",
      "inspector2:ListCoverage",
      "inspector2:GetFindingsReportStatus",
      "inspector:ListFindings",
      "inspector:DescribeFindings"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_policy" "cronjob-ecr-security-reporter-policy" {
  name        = "cronjob-ecr-security-reporter-policy"
  description = "Access secret-manager / ECR"
  policy      = data.aws_iam_policy_document.cronjob-ecr-security-reporter-doc.json
}