data "aws_iam_policy_document" "cronjob-status-changer-policy-doc" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
    ]
  }
}

resource "aws_iam_policy" "cronjob-status-changer-policy" {
  name        = "cronjob-status-changer-policy"
  description = "Access secret-manager"
  policy      = data.aws_iam_policy_document.cronjob-status-changer-policy-doc.json
}