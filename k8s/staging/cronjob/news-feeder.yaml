---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: opennews-news-feeder-cronjob
  namespace: staging
spec:
  schedule: "*/5 0-12 * * 1-5"
  jobTemplate:
    spec:
      backoffLimit: 3
      template:
        metadata:
          labels:
            run: opennews-news-feeder-cronjob
        spec:
          serviceAccountName: news-feeder-account
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          containers:
          - name: opennews-news-feeder-cronjob
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/cronjob-news-feeder:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
              - name: BU_NAME
                value: "OPENNEWS"
          restartPolicy: Never
  concurrencyPolicy: Allow
  successfulJobsHistoryLimit: 1
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: books-news-feeder-cronjob
  namespace: staging
spec:
  schedule: "0 0 * * 1-5"
  jobTemplate:
    spec:
      backoffLimit: 3
      template:
        metadata:
          labels:
            run: books-news-feeder-cronjob
        spec:
          serviceAccountName: news-feeder-account
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          containers:
          - name: books-news-feeder-cronjob
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/cronjob-news-feeder:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
              - name: BU_NAME
                value: "BOOKS"
          restartPolicy: Never
  concurrencyPolicy: Allow
  successfulJobsHistoryLimit: 1