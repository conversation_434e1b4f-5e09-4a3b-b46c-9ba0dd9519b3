apiVersion: batch/v1
kind: CronJob
metadata:
  name: weather-feeder-cronjob
  namespace: staging
spec:
  schedule: "0,30 0-11 * * 1-5"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: weather-feeder-cronjob
        spec:
          serviceAccountName: weather-account
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          containers:
          - name: weather-feeder-cronjob
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/cronjob-weather-feeder:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1