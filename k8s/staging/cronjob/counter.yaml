apiVersion: batch/v1
kind: CronJob
metadata:
  name: counter-cronjob
  namespace: staging
spec:
  schedule: "*/10 0-12 * * 1-5"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: counter-cronjob
        spec:
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          serviceAccountName: counter-account
          containers:
          - name: cronjob-counter
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/cronjob-counter:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1