data "aws_iam_policy_document" "cronjob-campaign-report-policy-doc" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret",
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx"
    ]
  }
}

resource "aws_iam_policy" "cronjob-campaign-report-policy" {
  name        = "cronjob-campaign-report-policy"
  description = "Access secret-manager / document_db"
  policy      = data.aws_iam_policy_document.cronjob-campaign-report-policy-doc.json
}
