data "aws_iam_policy_document" "cronjob-store-product-fetch-policy-doc" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret",
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:uat-seven-eleven-sftp-pwd-STK0dW"
    ]
  }
}

resource "aws_iam_policy" "cronjob-store-product-fetch-policy" {
  name        = "cronjob-store-product-fetch-policy"
  description = "Access secret-manager / document_db"
  policy      = data.aws_iam_policy_document.cronjob-store-product-fetch-policy-doc.json
}
