apiVersion: batch/v1
kind: CronJob
metadata:
  name: store-product-fetch-cronjob
  namespace: staging
spec:
  schedule: "0 1 * * *"
  startingDeadlineSeconds: 7200
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: store-product-fetch-cronjob
        spec:
          serviceAccountName: store-product-fetch-account
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          containers:
          - name: store-product-fetch-cronjob
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/cronjob-store-product-fetch:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1