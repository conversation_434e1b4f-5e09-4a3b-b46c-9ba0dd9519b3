apiVersion: batch/v1
kind: CronJob
metadata:
  name: banner-generator-cronjob
  namespace: staging
spec:
  schedule: "0,30 0-12 * * 1-5"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: banner-generator-cronjob
        spec:
          serviceAccountName: banner-generator-account
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          containers:
          - name: banner-generator-cronjob
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/banner-generator:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1