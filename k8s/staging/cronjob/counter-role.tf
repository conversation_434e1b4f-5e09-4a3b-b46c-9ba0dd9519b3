data "aws_iam_policy_document" "cronjob-counter-policy-doc" {
 statement {
    actions = [
      "sqs:GetQueueUrl",
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:GetQueueAttributes",
    ]
    resources = [
      "arn:aws:sqs:ap-northeast-1:471112919450:counter-queue"
    ]
  }
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret",
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F"
    ]
  }
}

resource "aws_iam_policy" "cronjob-counter-policy" {
  name        = "cronjob-counter-policy"
  description = "Access secret-manager / counter-queue / document_db"
  policy      = data.aws_iam_policy_document.cronjob-counter-policy-doc.json
}