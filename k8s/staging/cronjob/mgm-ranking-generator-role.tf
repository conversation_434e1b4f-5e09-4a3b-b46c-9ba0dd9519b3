data "aws_iam_policy_document" "cronjob-mgm-ranking-generator-policy-doc" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
    ]
  }
  statement {
    actions = [
      "cloudfront:CreateInvalidation"
    ]
    resources = [
      "*",
    ]
  }
  statement {
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:PutObjectAcl",
      "s3:GetObjectAcl",
      "s3:DeleteObject",
    ]
    resources = [
      "arn:aws:s3:::uat-image/*",
    ]
  }
  statement {
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      "arn:aws:s3:::uat-image",
    ]
  }
}

resource "aws_iam_policy" "cronjob-mgm-ranking-generator-policy" {
  name        = "cronjob-mgm-ranking-generator-policy"
  description = "Access secret-manager"
  policy      = data.aws_iam_policy_document.cronjob-mgm-ranking-generator-policy-doc.json
}