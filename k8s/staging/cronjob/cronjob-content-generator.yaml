apiVersion: batch/v1
kind: CronJob
metadata:
  name: cronjob-content-generator
  namespace: staging
spec:
  schedule: "15,45 0-12 * * 1-5"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: cronjob-content-generator
        spec:
          serviceAccountName: banner-generator-account
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          containers:
          - name: cronjob-content-generator
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/cronjob-content-generator:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1