apiVersion: batch/v1
kind: CronJob
metadata:
  name: mgm-ranking-generator-cronjob
  namespace: staging
spec:
  schedule: "3 1 * * 1-5"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: mgm-ranking-generator-cronjob
        spec:
          serviceAccountName: mgm-ranking-generator-account
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          containers:
          - name: cronjob-mgm-ranking-generator
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/cronjob-mgm-ranking-generator:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1