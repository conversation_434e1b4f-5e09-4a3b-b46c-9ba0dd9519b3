apiVersion: batch/v1
kind: CronJob
metadata:
  name: cronjob-ec-generator
  namespace: staging
spec:
  schedule: "*/5 0-12 * * 1-5"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            run: cronjob-ec-generator
        spec:
          serviceAccountName: ec-generator-account
          nodeSelector:
            nodegroup: "cronjob"
          tolerations:
            - key: "cronjobOnly"
              operator: "Equal"
              value: "true"
              effect: "NoSchedule"
          containers:
          - name: cronjob-ec-generator
            image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/cronjob-ec-generator:latest
            imagePullPolicy: Always
            env:
              - name: ENV
                value: "staging"
          restartPolicy: Never
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1