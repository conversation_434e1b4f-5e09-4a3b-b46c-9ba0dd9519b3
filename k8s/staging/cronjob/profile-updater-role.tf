data "aws_iam_policy_document" "cronjob-profile-updater-policy-doc" {
 statement {
    actions = [
      "sqs:GetQueueUrl",
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:GetQueueAttributes",
    ]
    resources = [
      "arn:aws:sqs:ap-northeast-1:471112919450:profile-updater-queue"
    ]
  }
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret",
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F"
    ]
  }
}

resource "aws_iam_policy" "cronjob-profile-updater-policy" {
  name        = "cronjob-profile-updater-policy"
  description = "Access secret-manager / profile-updater-queue / document_db"
  policy      = data.aws_iam_policy_document.cronjob-profile-updater-policy-doc.json
}