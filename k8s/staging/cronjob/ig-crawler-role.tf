data "aws_iam_policy_document" "cronjob-ig-crawler-policy-doc" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:instagram-user-access-token-AFfTkv"
    ]
  }
  statement {
    actions = [
      "cloudfront:CreateInvalidation"
    ]
    resources = [
      "*",
    ]
  }
  statement {
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:PutObjectAcl",
      "s3:GetObjectAcl",
      "s3:DeleteObject"
    ]
    resources = [
      "arn:aws:s3:::uat-image/*",
    ]
  }
}

resource "aws_iam_policy" "cronjob-ig-crawler-policy" {
  name        = "cronjob-ig-crawler-policy"
  description = "Access secret-manager / s3 / cloudfront"
  policy      = data.aws_iam_policy_document.cronjob-ig-crawler-policy-doc.json
}