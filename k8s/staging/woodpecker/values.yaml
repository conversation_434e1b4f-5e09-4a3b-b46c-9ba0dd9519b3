env:
  # 請替換為你的實際網址（需加上 HTTPS）
  WOODPECKER_HOST: https://ci.dev.uniopen.com

  # GitHub OAuth
  WOODPECKER_GITHUB: true
  WOODPECKER_AGENT_SECRET: 8QsyW4Auli4wZOl
  WOODPECKER_GITHUB_CLIENT: ********************
  WOODPECKER_GITHUB_SECRET: ****************************************
  WOODPECKER_ADMIN: roger-wang-uni

  # 是否允許所有 GitHub 使用者登入
  WOODPECKER_OPEN: true

# 不使用 Helm 產生的 Ingress（你自己整合）
server:
  ingress:
    enabled: false

agent:
  enabled: true
  env:
    WOODPECKER_BACKEND: kubernetes
    WOODPECKER_AGENT_SECRET: 8QsyW4Auli4wZOl  # ⬅️ 必須與上面相同
    WOODPECKER_KUBERNETES_NAMESPACE: woodpecker
    WOODPECKER_KUBERNETES_STORAGE_CLASS: gp2     # ⬅️ 你的 EBS StorageClass 名稱（可查詢 `kubectl get sc`）

  serviceAccount:
    create: false  # 若你已透過 IRSA 預先建立

  # 若你要手動指定 serviceAccount（如 IRSA），加上這個欄位：
  # name: woodpecker-agent

persistence:
  enabled: false  # 若你有設定 volume 可開啟
