# https://docs.aws.amazon.com/eks/latest/userguide/fargate-logging.html
kind: ConfigMap
apiVersion: v1
metadata:
  name: aws-logging
  namespace: aws-observability
data:
  flb_log_cw: "true"  # Set to true to ship Fluent Bit process logs to CloudWatch.
  filters.conf: |
    [FILTER]
        Name kubernetes
        Match kube.*
        Merge_Log On
        Keep_Log Off
        Buffer_Size 0
        Kube_Meta_Cache_TTL 300s
#    [FILTER]
#        Name parser
#        Match *
#        Key_name log
#        Parser crio

  output.conf: |
    [OUTPUT]
        Name cloudwatch_logs
        Match   kube.*
        region ap-northeast-1
        log_group_name uniopen-staging-services
        log_stream_prefix from-fluent-bit-
        log_retention_days 60
        auto_create_group true
#  parsers.conf: |
#    [PARSER]
#        Name crio
#        Format Regex
#        Regex ^(?<time>[^ ]+) (?<stream>stdout|stderr) (?<logtag>P|F) (?<log>.*)$
#        Time_Key    time
#        Time_Format %Y-%m-%dT%H:%M:%S.%L%z