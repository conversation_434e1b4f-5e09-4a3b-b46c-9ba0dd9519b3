---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: opinion
  namespace: staging
spec:
  selector:
    matchLabels:
      run: opinion
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: opinion
    spec:
      serviceAccountName: opinion-account
      nodeSelector:
        nodegroup: "services"
      tolerations:
        - key: "servicesOnly"
          operator: "Equal"
          value: "true"
          effect: "NoSchedule"
      containers:
        - name: opinion
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/opinion:latest
          imagePullPolicy: Always
          env:
            - name: CONFIG_PATH
              value: "/app/config/staging.yaml"
            - name: ENV
              value: staging
          ports:
            - containerPort: 8000
          resources:
            limits:
              cpu: 250m
              memory: 256Mi
            requests:
              cpu: 250m
              memory: 256Mi

          startupProbe:
            httpGet:
              path: /healthcheck
              port: 8000
              scheme: HTTP
            failureThreshold: 30
            periodSeconds: 10

          livenessProbe:
            httpGet:
              path: /healthcheck
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 30
            successThreshold: 1
            failureThreshold: 3

          readinessProbe:
            httpGet:
              path: /healthcheck
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 30
            timeoutSeconds: 30
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3