---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: point-center
  namespace: staging
spec:
  selector:
    matchLabels:
      run: point-center
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: point-center
    spec:
      #serviceAccountName: www-account
      nodeSelector:
        nodegroup: "services"
      tolerations:
        - key: "servicesOnly"
          operator: "Equal"
          value: "true"
          effect: "NoSchedule"
      containers:
        - name: point-center
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/point-center:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
          env:
            - name: ENV
              value: staging
          resources:
            limits:
              cpu: 250m
              memory: 256Mi
            requests:
              cpu: 250m
              memory: 256Mi
