{"version": 4, "terraform_version": "1.5.7", "serial": 1, "lineage": "e6255ba4-4c10-0711-e96b-84e2d9dbe6f7", "outputs": {}, "resources": [{"mode": "data", "type": "aws_iam_policy_document", "name": "www-turborepo-remote-cache-server-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "695315080", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:turborepo-remote-cache-token-NVJesP\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"s3:PutObject\",\n        \"s3:ListBucket\",\n        \"s3:GetObject\",\n        \"s3:DeleteObject\"\n      ],\n      \"Resource\": [\n        \"arn:aws:s3:::f2e-turborepo-cache/*\",\n        \"arn:aws:s3:::f2e-turborepo-cache\"\n      ]\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:turborepo-remote-cache-token-NVJesP\"},{\"Effect\":\"Allow\",\"Action\":[\"s3:PutObject\",\"s3:ListBucket\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Resource\":[\"arn:aws:s3:::f2e-turborepo-cache/*\",\"arn:aws:s3:::f2e-turborepo-cache\"]}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:turborepo-remote-cache-token-NVJesP"], "sid": ""}, {"actions": ["s3:DeleteObject", "s3:GetObject", "s3:ListBucket", "s3:PutObject"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::f2e-turborepo-cache", "arn:aws:s3:::f2e-turborepo-cache/*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "www-turborepo-remote-cache-server_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/www-turborepo-remote-cache-server_policy", "attachment_count": 0, "description": "Allow turborepo-remote-cache-server to access S3 and get TURBO_TOKEN from secrets manager", "id": "arn:aws:iam::471112919450:policy/www-turborepo-remote-cache-server_policy", "name": "www-turborepo-remote-cache-server_policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:turborepo-remote-cache-token-NVJesP\"},{\"Action\":[\"s3:PutObject\",\"s3:ListBucket\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::f2e-turborepo-cache/*\",\"arn:aws:s3:::f2e-turborepo-cache\"]}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNKOQ65HQEX", "tags": null, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.www-turborepo-remote-cache-server-policy-doc"]}]}], "check_results": null}