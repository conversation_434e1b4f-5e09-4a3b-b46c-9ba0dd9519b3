apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: turborepo-remote-cache-secrets
  namespace: staging
spec:
  provider: aws
  secretObjects:
    - secretName: turborepo-remote-cache-secrets-env
      type: Opaque
      data:
        - objectName: "turborepo-remote-cache-token-NVJesP"
          key: "TURBO_TOKEN"
  parameters:
    objects: |
      - objectName: "turborepo-remote-cache-token-NVJesP"
        objectType: "secretsmanager"
