---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: turborepo-remote-cache-server
  namespace: staging
spec:
  selector:
    matchLabels:
      run: turborepo-remote-cache-server
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: turborepo-remote-cache-server
    spec:
      nodeSelector:
        nodegroup: "services"
      tolerations:
        - key: "servicesOnly"
          operator: "Equal"
          value: "true"
          effect: "NoSchedule"
      serviceAccountName: turborepo-remote-cache-server-account
      containers:
        - name: turborepo-remote-cache-server
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/turborepo-remote-cache-server:latest
          imagePullPolicy: Always
          env:
            - name: NODE_ENV
              value: "production"
            - name: PORT
              value: "3000"
            - name: LOG_LEVEL
              value: "info"
            - name: STORAGE_PROVIDER
              value: "s3"
            - name: STORAGE_PATH
              value: "f2e-turborepo-cache"
            - name: AWS_REGION
              value: "ap-northeast-1"
          envFrom:
            - secretRef:
                name: turborepo-remote-cache-secrets-env
          volumeMounts:
            - name: secrets-store-env
              mountPath: "/mnt/secrets-store"
              readOnly: true
          ports:
            - containerPort: 3000
          resources:
            limits:
              cpu: 250m
              memory: 256Mi
            requests:
              cpu: 250m
              memory: 256Mi
          startupProbe:
            httpGet:
              path: /healthcheck
              port: 3000
              scheme: HTTP
            failureThreshold: 3
            periodSeconds: 10
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthcheck
              port: 3000
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthcheck
              port: 3000
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
      volumes:
        - name: secrets-store-env
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: "turborepo-remote-cache-secrets"