---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: turborepo-remote-cache-server
  namespace: staging
spec:
  selector:
    matchLabels:
      run: turborepo-remote-cache-server
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: turborepo-remote-cache-server
    spec:
      nodeSelector:
        nodegroup: "services"
      tolerations:
        - key: "servicesOnly"
          operator: "Equal"
          value: "true"
          effect: "NoSchedule"
      serviceAccountName: turborepo-remote-cache-server-account
      containers:
        - name: turborepo-remote-cache-server
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/turborepo-remote-cache-server:latest
          imagePullPolicy: Always
          env:
            - name: CONFIG_PATH
              value: /app/config/staging.yaml
            - name: ENV
              value: "staging"
          ports:
            - containerPort: 3000
          resources:
            limits:
              cpu: 250m
              memory: 256Mi
            requests:
              cpu: 250m
              memory: 256Mi
          startupProbe:
            httpGet:
              path: /healthcheck
              port: 3000
              scheme: HTTP
            failureThreshold: 3
            periodSeconds: 10
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthcheck
              port: 3000
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthcheck
              port: 3000
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1