provider "aws" {
  region  = "ap-northeast-1"
  profile = "staging"
}
data "aws_iam_policy_document" "www-turborepo-remote-cache-server-policy-doc" {

  statement {
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:ListBucket",
      "s3:DeleteObject"
    ]
    resources = [
      "arn:aws:s3:::f2e-turborepo-cache",
      "arn:aws:s3:::f2e-turborepo-cache/*"
    ]
  }
}

resource "aws_iam_policy" "www-turborepo-remote-cache-server_policy" {
  name        = "www-turborepo-remote-cache-server_policy"
  description = "Allow turborepo-remote-cache-server to access S3"
  policy      = data.aws_iam_policy_document.www-turborepo-remote-cache-server-policy-doc.json
}
