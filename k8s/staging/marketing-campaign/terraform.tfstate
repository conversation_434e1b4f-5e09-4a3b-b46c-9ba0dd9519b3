{"version": 4, "terraform_version": "1.10.1", "serial": 16, "lineage": "0393f14d-faab-6de3-5cfb-72e65566ecb2", "outputs": {}, "resources": [{"mode": "data", "type": "aws_iam_policy_document", "name": "CodeBuildBasePolicy-marketing-campaign-ap-northeast-1", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"secretsmanager:GetSecretValue\",\n        \"secretsmanager:DescribeSecret\"\n      ],\n      \"Resource\": [\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-inline-api-key-2krFpl\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-google-captcha-key-emf89V\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII\",\n        \"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"appconfig:StartConfigurationSession\",\n        \"appconfig:GetLatestConfiguration\"\n      ],\n      \"Resource\": [\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\n        \"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"\n      ]\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-inline-api-key-2krFpl\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-google-captcha-key-emf89V\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Effect\":\"Allow\",\"Action\":[\"appconfig:StartConfigurationSession\",\"appconfig:GetLatestConfiguration\"],\"Resource\":[\"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"]}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-google-captcha-key-emf89V", "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-inline-api-key-2krFpl"], "sid": ""}, {"actions": ["appconfig:GetLatestConfiguration", "appconfig:StartConfigurationSession"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:appconfig:ap-northeast-1:471112919450:application/*", "arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*", "arn:aws:appconfig:ap-northeast-1:471112919450:environment/*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "marketing_campaign_acm_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/marketing_campaign_auth_code_policy", "attachment_count": 1, "description": "Access to Weather API Code", "id": "arn:aws:iam::471112919450:policy/marketing_campaign_auth_code_policy", "name": "marketing_campaign_auth_code_policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\",\"secretsmanager:DescribeSecret\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-inline-api-key-2krFpl\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-google-captcha-key-emf89V\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII\",\"arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F\"]},{\"Action\":[\"appconfig:StartConfigurationSession\",\"appconfig:GetLatestConfiguration\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:appconfig:ap-northeast-1:471112919450:environment/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*\",\"arn:aws:appconfig:ap-northeast-1:471112919450:application/*\"]}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNGNVZSGWJU", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.CodeBuildBasePolicy-marketing-campaign-ap-northeast-1"]}]}], "check_results": null}