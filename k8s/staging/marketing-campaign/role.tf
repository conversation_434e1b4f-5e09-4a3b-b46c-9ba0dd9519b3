provider "aws" {
  region = "ap-northeast-1"
  profile = "default"
}
data "aws_iam_policy_document" "CodeBuildBasePolicy-marketing-campaign-ap-northeast-1" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-auth-jwt-secret-token-server-8fHIII",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-google-captcha-key-emf89V",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-inline-api-key-2krFpl"
    ]
  }
  statement {
    effect = "Allow"
    actions = [
      "appconfig:StartConfigurationSession",
      "appconfig:GetLatestConfiguration"
    ]
    resources = [
        "arn:aws:appconfig:ap-northeast-1:471112919450:application/*",
        "arn:aws:appconfig:ap-northeast-1:471112919450:environment/*",
        "arn:aws:appconfig:ap-northeast-1:471112919450:configurationprofile/*"
      ]
  }
}



resource "aws_iam_policy" "marketing_campaign_acm_policy" {
  name        = "marketing_campaign_auth_code_policy"
  description = "Access to Weather API Code"
  policy      = data.aws_iam_policy_document.CodeBuildBasePolicy-marketing-campaign-ap-northeast-1.json
}