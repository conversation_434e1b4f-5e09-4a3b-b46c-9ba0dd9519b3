---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mbox
  namespace: staging
spec:
  selector:
    matchLabels:
      run: mbox
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: mbox
        compute-type: fargate
    spec:
      #serviceAccountName: mbox-irsa-account
      containers:
        - name: mbox
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/mbox:latest
          imagePullPolicy: Always
          env:
            - name: ENV
              value: "staging"
          ports:
            - containerPort: 8000
          resources:
            limits:
              cpu: 1000m
              memory: 2048Mi
            requests:
              cpu: 700m
              memory: 1024Mi