apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-api
  namespace: staging
spec:
  selector:
    matchLabels:
      run: admin-api
  # remove replica if using gitops
  replicas: 1
  template:
    metadata:
      labels:
        run: admin-api
    spec:
      serviceAccountName: admin-api-account
      nodeSelector:
        nodegroup: "services"
      tolerations:
        - key: "servicesOnly"
          operator: "Equal"
          value: "true"
          effect: "NoSchedule"
      containers:
        - name: admin-api
          image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/admin-api:latest
          imagePullPolicy: Always
          env:
            - name: CONFIG_PATH
              value: "/app/config/staging.yaml"
            - name: ENV
              value: staging
          ports:
            - containerPort: 8000
          resources:
            limits:
              cpu: 1000m
              memory: 1024Mi
            requests:
              cpu: 500m
              memory: 512Mi
          startupProbe:
            httpGet:
              path: /healthcheck
              port: 8000
              scheme: HTTP
            failureThreshold: 3
            periodSeconds: 10
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthcheck
              port: 8000
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthcheck
              port: 8000
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1