{"version": 4, "terraform_version": "1.10.4", "serial": 41, "lineage": "632a5352-5e19-803c-410c-1069f906da67", "outputs": {}, "resources": [{"mode": "data", "type": "aws_iam_policy_document", "name": "admin-api-s3-policy-doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"s3:PutObject\",\n        \"s3:ListBucket\",\n        \"s3:GetObject\",\n        \"s3:DeleteObject\"\n      ],\n      \"Resource\": [\n        \"arn:aws:s3:::uat-image/temp_file/*\",\n        \"arn:aws:s3:::uat-image/store_images/*\",\n        \"arn:aws:s3:::uat-image/store_campaign/*\",\n        \"arn:aws:s3:::uat-image/static_resources/store_campaigns/*\",\n        \"arn:aws:s3:::uat-image/static_resources/flagship_templates/*\",\n        \"arn:aws:s3:::uat-image/static_resources/event/*\",\n        \"arn:aws:s3:::uat-image/quizzes/*\",\n        \"arn:aws:s3:::uat-image/opinion/*\",\n        \"arn:aws:s3:::uat-image/op_promotions/*\",\n        \"arn:aws:s3:::uat-image/news_articles/*\",\n        \"arn:aws:s3:::uat-image/mod*\",\n        \"arn:aws:s3:::uat-image/flagship_templates/*\",\n        \"arn:aws:s3:::uat-image/coupons/*\",\n        \"arn:aws:s3:::uat-image/categorybanners/*\",\n        \"arn:aws:s3:::uat-image/businessUnit/*\",\n        \"arn:aws:s3:::uat-image/brand_logos/*\",\n        \"arn:aws:s3:::uat-image/announcements/*\",\n        \"arn:aws:s3:::uat-image\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"cloudfront:CreateInvalidation\",\n      \"Resource\": \"*\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"s3:PutObject\",\"s3:ListBucket\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Resource\":[\"arn:aws:s3:::uat-image/temp_file/*\",\"arn:aws:s3:::uat-image/store_images/*\",\"arn:aws:s3:::uat-image/store_campaign/*\",\"arn:aws:s3:::uat-image/static_resources/store_campaigns/*\",\"arn:aws:s3:::uat-image/static_resources/flagship_templates/*\",\"arn:aws:s3:::uat-image/static_resources/event/*\",\"arn:aws:s3:::uat-image/quizzes/*\",\"arn:aws:s3:::uat-image/opinion/*\",\"arn:aws:s3:::uat-image/op_promotions/*\",\"arn:aws:s3:::uat-image/news_articles/*\",\"arn:aws:s3:::uat-image/mod*\",\"arn:aws:s3:::uat-image/flagship_templates/*\",\"arn:aws:s3:::uat-image/coupons/*\",\"arn:aws:s3:::uat-image/categorybanners/*\",\"arn:aws:s3:::uat-image/businessUnit/*\",\"arn:aws:s3:::uat-image/brand_logos/*\",\"arn:aws:s3:::uat-image/announcements/*\",\"arn:aws:s3:::uat-image\"]},{\"Effect\":\"Allow\",\"Action\":\"cloudfront:CreateInvalidation\",\"Resource\":\"*\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["s3:DeleteObject", "s3:GetObject", "s3:ListBucket", "s3:PutObject"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::uat-image", "arn:aws:s3:::uat-image/announcements/*", "arn:aws:s3:::uat-image/brand_logos/*", "arn:aws:s3:::uat-image/businessUnit/*", "arn:aws:s3:::uat-image/categorybanners/*", "arn:aws:s3:::uat-image/coupons/*", "arn:aws:s3:::uat-image/flagship_templates/*", "arn:aws:s3:::uat-image/mod*", "arn:aws:s3:::uat-image/news_articles/*", "arn:aws:s3:::uat-image/op_promotions/*", "arn:aws:s3:::uat-image/opinion/*", "arn:aws:s3:::uat-image/quizzes/*", "arn:aws:s3:::uat-image/static_resources/event/*", "arn:aws:s3:::uat-image/static_resources/flagship_templates/*", "arn:aws:s3:::uat-image/static_resources/store_campaigns/*", "arn:aws:s3:::uat-image/store_campaign/*", "arn:aws:s3:::uat-image/store_images/*", "arn:aws:s3:::uat-image/temp_file/*"], "sid": ""}, {"actions": ["cloudfront:CreateInvalidation"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "refresh_banner_sqs_policy_doc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"sqs:SendMessage\",\n        \"sqs:ReceiveMessage\",\n        \"sqs:GetQueueUrl\",\n        \"sqs:GetQueueAttributes\",\n        \"sqs:DeleteMessage\"\n      ],\n      \"Resource\": [\n        \"arn:aws:sqs:ap-northeast-1:471112919450:refresh_banner_queue.fifo\",\n        \"arn:aws:sqs:ap-northeast-1:471112919450:member-campaign-event-queue\"\n      ]\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"sqs:SendMessage\",\"sqs:ReceiveMessage\",\"sqs:GetQueueUrl\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\"],\"Resource\":[\"arn:aws:sqs:ap-northeast-1:471112919450:refresh_banner_queue.fifo\",\"arn:aws:sqs:ap-northeast-1:471112919450:member-campaign-event-queue\"]}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["sqs:DeleteMessage", "sqs:Get<PERSON>ueueAttributes", "sqs:GetQueueUrl", "sqs:ReceiveMessage", "sqs:SendMessage"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:sqs:ap-northeast-1:471112919450:member-campaign-event-queue", "arn:aws:sqs:ap-northeast-1:471112919450:refresh_banner_queue.fifo"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "admin-api-s3-policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/admin-api-s3-policy", "attachment_count": 1, "description": "Access to S3", "id": "arn:aws:iam::471112919450:policy/admin-api-s3-policy", "name": "admin-api-s3-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"s3:PutObject\",\"s3:ListBucket\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::uat-image/temp_file/*\",\"arn:aws:s3:::uat-image/store_images/*\",\"arn:aws:s3:::uat-image/store_campaign/*\",\"arn:aws:s3:::uat-image/static_resources/store_campaigns/*\",\"arn:aws:s3:::uat-image/static_resources/flagship_templates/*\",\"arn:aws:s3:::uat-image/static_resources/event/*\",\"arn:aws:s3:::uat-image/quizzes/*\",\"arn:aws:s3:::uat-image/opinion/*\",\"arn:aws:s3:::uat-image/op_promotions/*\",\"arn:aws:s3:::uat-image/news_articles/*\",\"arn:aws:s3:::uat-image/mod*\",\"arn:aws:s3:::uat-image/flagship_templates/*\",\"arn:aws:s3:::uat-image/coupons/*\",\"arn:aws:s3:::uat-image/categorybanners/*\",\"arn:aws:s3:::uat-image/businessUnit/*\",\"arn:aws:s3:::uat-image/brand_logos/*\",\"arn:aws:s3:::uat-image/announcements/*\",\"arn:aws:s3:::uat-image\"]},{\"Action\":\"cloudfront:CreateInvalidation\",\"Effect\":\"Allow\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNFVSKQ5FSV", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.admin-api-s3-policy-doc"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "admin_api_sqs_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::471112919450:policy/admin_api_sqs_policy", "attachment_count": 1, "description": "Access to SQS", "id": "arn:aws:iam::471112919450:policy/admin_api_sqs_policy", "name": "admin_api_sqs_policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"sqs:SendMessage\",\"sqs:ReceiveMessage\",\"sqs:GetQueueUrl\",\"sqs:GetQueueAttributes\",\"sqs:DeleteMessage\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:sqs:ap-northeast-1:471112919450:refresh_banner_queue.fifo\",\"arn:aws:sqs:ap-northeast-1:471112919450:member-campaign-event-queue\"]}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAW3MEEVGNP5AP47H6T", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_iam_policy_document.refresh_banner_sqs_policy_doc"]}]}], "check_results": null}