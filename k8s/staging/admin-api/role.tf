provider "aws" {
  region  = "ap-northeast-1"
  profile = "staging"
}

data "aws_iam_policy_document" "refresh_banner_sqs_policy_doc" {
  statement {
    actions = [
      "sqs:SendMessage",
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:GetQueueAttributes",
      "sqs:GetQueueUrl"
    ]
    resources = [
      "arn:aws:sqs:ap-northeast-1:471112919450:refresh_banner_queue.fifo",
      "arn:aws:sqs:ap-northeast-1:471112919450:member-campaign-event-queue"
    ]
  }
}

resource "aws_iam_policy" "admin_api_sqs_policy" {
  name        = "admin_api_sqs_policy"
  description = "Access to SQS"
  policy      = data.aws_iam_policy_document.refresh_banner_sqs_policy_doc.json
}

data "aws_iam_policy_document" "admin-api-s3-policy-doc" {
  statement {
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:ListBucket",
      "s3:DeleteObject"
    ]
    resources = [
      "arn:aws:s3:::uat-image/brand_logos/*",
      "arn:aws:s3:::uat-image/news_articles/*",
      "arn:aws:s3:::uat-image/mod*",
      "arn:aws:s3:::uat-image/temp_file/*",
      "arn:aws:s3:::uat-image/opinion/*",
      "arn:aws:s3:::uat-image/announcements/*",
      "arn:aws:s3:::uat-image/businessUnit/*",
      "arn:aws:s3:::uat-image/flagship_templates/*",
      "arn:aws:s3:::uat-image/static_resources/flagship_templates/*",
      "arn:aws:s3:::uat-image/static_resources/store_campaigns/*",
      "arn:aws:s3:::uat-image/static_resources/event/*",
      "arn:aws:s3:::uat-image/store_campaign/*",
      "arn:aws:s3:::uat-image/categorybanners/*",
      "arn:aws:s3:::uat-image/quizzes/*",
      "arn:aws:s3:::uat-image/op_promotions/*",
      "arn:aws:s3:::uat-image/coupons/*",
      "arn:aws:s3:::uat-image",
      "arn:aws:s3:::uat-image/store_images/*",
    ]
  }
  statement {
    actions = [
      "cloudfront:CreateInvalidation"
    ]
    resources = [
      "*"
    ]
  }
}

resource "aws_iam_policy" "admin-api-s3-policy" {
  name        = "admin-api-s3-policy"
  description = "Access to S3"
  policy      = data.aws_iam_policy_document.admin-api-s3-policy-doc.json
}
