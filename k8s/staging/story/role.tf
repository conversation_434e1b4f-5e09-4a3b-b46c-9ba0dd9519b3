provider "aws" {
  region = "ap-northeast-1"
  profile = "staging"
}
data "aws_iam_policy_document" "story-policy-doc" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:document_db_pwd-qxHR3F",
      "arn:aws:secretsmanager:ap-northeast-1:471112919450:secret:staging-cache-auth-token-BGpNsx"
    ]
  }

  statement {
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:ListBucket",
      "s3:DeleteObject"
    ]
    resources = [
      "arn:aws:s3:::uat-image/social/*",
      "arn:aws:s3:::uat-image"
    ]
  }
}

resource "aws_iam_policy" "story_policy" {
  name        = "story_policy"
  description = "Access to story API Code"
  policy      = data.aws_iam_policy_document.story-policy-doc.json
}