provider "kubernetes" {
  config_path = "~/.kube/config"
}

provider "aws" {
  region = "ap-northeast-1"
  profile = "staging"
}
################################################################################
# IAM Roles for Service Accounts (IRSA)
################################################################################
locals {
  eks_oidc_provider_arn = "arn:aws:iam::************:oidc-provider/oidc.eks.ap-northeast-1.amazonaws.com/id/B3DE07E3C89D23D8EC6AA8DFD0272007"

  eks_roles = {
    weather = {
      sa_name   = "weather-account"
      role_name = "weather-assume-role"
      policies = {
        weather_auth_code = "arn:aws:iam::************:policy/weather_auth_code_policy"
      }
    }
    news = {
      sa_name   = "news-account"
      role_name = "news-assume-role"
      policies = {
        news_sha_key = "arn:aws:iam::************:policy/staging_news_sha_key"
      }
    }
    member = {
      sa_name = "member-account"
      role_name = "member-assume-role"
      policies = {
        member = "arn:aws:iam::************:policy/staging-member-policy"
      }
    }
    media = {
      sa_name   = "media-account"
      role_name = "media-assume-role"
      policies = {
        media_keys = "arn:aws:iam::************:policy/media_keys_policy"
      }
    }
    opinion = {
      sa_name   = "opinion-account"
      role_name = "opinion-assume-role"
      policies = {
        opinion_s3 = "arn:aws:iam::************:policy/opinion-s3-policy"
        policy     = "arn:aws:iam::************:policy/staging-opinion-policy"
      }
    }
    ecsearch = {
      sa_name   = "ecsearch-account"
      role_name = "ecsearch-assume-role"
      policies = {
        ecsearch_keys = "arn:aws:iam::************:policy/staging-ecsearch-policy"
      }
    }
    ecfeeder = {
      sa_name   = "ecfeeder-account"
      role_name = "ecfeeder-assume-role"
      policies = {
        ecsearch_keys = "arn:aws:iam::************:policy/staging-ecfeeder-policy"
      }
    }
    redirect = {
      sa_name   = "redirect-account"
      role_name = "redirect-assume-role"
      policies = {
        ecsearch_keys = "arn:aws:iam::************:policy/redirect_policy"
      }
    }
    location = {
      sa_name   = "location-account"
      role_name = "location-assume-role"
      policies = {
        ecsearch_keys = "arn:aws:iam::************:policy/location-keys-policy"
      }
    }
    picasso = {
      sa_name   = "picasso-account"
      role_name = "picasso-assume-role"
      policies = {
        picasso_s3 = "arn:aws:iam::************:policy/picasso-s3-policy"
      }
    }
    authz = {
      sa_name   = "authz-account"
      role_name = "authz-assume-role"
      policies = {
        cross_role = "arn:aws:iam::************:policy/authz-cross-role-policy"
        authz_iam  = "arn:aws:iam::************:policy/authz-iam-policy"
        member = "arn:aws:iam::************:policy/staging-member-policy"
      }
    }
    admin-api = {
      sa_name   = "admin-api-account"
      role_name = "admin-api-assume-role"
      policies = {
        admin_api_s3  = "arn:aws:iam::************:policy/admin-api-s3-policy"
        admin_api_key = "arn:aws:iam::************:policy/admin-api_keys_policy"
        admin_api_sqs = "arn:aws:iam::************:policy/admin_api_sqs_policy"
      }
    }
    coin = {
      sa_name   = "coin-account"
      role_name = "coin-assume-role"
      policies = {
        coin_policy = "arn:aws:iam::************:policy/coin_policy"
      }
    }
    story = {
      sa_name   = "story-account"
      role_name = "story-assume-role"
      policies = {
        story_policy = "arn:aws:iam::************:policy/story_policy"
      }
    }
    poi = {
      sa_name   = "poi-account"
      role_name = "poi-assume-role"
      policies = {
        poi_policy = "arn:aws:iam::************:policy/poi_policy"
      }
    }
    user = {
      sa_name   = "user-account"
      role_name = "user-assume-role"
      policies = {
        user_policy = "arn:aws:iam::************:policy/user_policy"
      }
    }
    strapi = {
      sa_name   = "strapi-account"
      role_name = "strapi-assume-role"
      policies = {
        strapi_policy = "arn:aws:iam::************:policy/strapi-policy"
      }
    }
    admin-coupon = {
      sa_name   = "admin-coupon-account"
      role_name = "admin-coupon-assume-role"
      policies = {
        admin_coupon_policy = "arn:aws:iam::************:policy/admin-coupon_policy"
      }
    }
    coupon = {
      sa_name   = "coupon-account"
      role_name = "coupon-assume-role"
      policies = {
        coupon_policy = "arn:aws:iam::************:policy/coupon_policy"
      }
    }
    marketing-campaign = {
      sa_name   = "marketing-campaign-account"
      role_name = "marketing-campaign-assume-role"
      policies = {
        keys      = "arn:aws:iam::************:policy/marketing_campaign_keys_policy"
        auth_code = "arn:aws:iam::************:policy/marketing_campaign_auth_code_policy"
      }
    }
    reservation = {
      sa_name   = "reservation-account"
      role_name = "reservation-assume-role"
      policies = {
        reservation_policy = "arn:aws:iam::************:policy/staging-reservation-policy"
      }
    }
    banner-generator = {
      sa_name   = "banner-generator-account"
      role_name = "banner-generator-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/cronjob-banner-generator-policy"
      }
    }
    campaign-report = {
      sa_name   = "campaign-report-account"
      role_name = "campaign-report-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/cronjob-campaign-report-policy"
      }
    }
    counter = {
      sa_name   = "counter-account"
      role_name = "counter-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/cronjob-counter-policy"
      }
    }
    ec-generator = {
      sa_name   = "ec-generator-account"
      role_name = "ec-generator-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/cronjob-ec-generator-policy"
      }
    }
    ecr-security-reporter = {
      sa_name   = "ecr-security-reporter-account"
      role_name = "ecr-security-reporter-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/cronjob-ecr-security-reporter-policy"
      }
    }
    ig-crawler = {
      sa_name   = "ig-crawler-account"
      role_name = "ig-crawler-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/cronjob-ig-crawler-policy"
      }
    }
    news-classify = {
      sa_name   = "news-classify-account"
      role_name = "news-classify-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/cronjob-news-classify-policy"
      }
    }
    news-feeder = {
      sa_name   = "news-feeder-account"
      role_name = "news-feeder-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/cronjob-news-feeder-policy"
      }
    }
    popularity-updater = {
      sa_name   = "popularity-updater-account"
      role_name = "popularity-updater-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/cronjob-popularity-updater-policy"
      }
    }
    profile-updater = {
      sa_name   = "profile-updater-account"
      role_name = "profile-updater-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/cronjob-profile-updater-policy"
      }
    }
    store-product-fetch = {
      sa_name   = "store-product-fetch-account"
      role_name = "store-product-fetch-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/cronjob-store-product-fetch-policy"
      }
    }
    token = {
      sa_name   = "token-service-account"
      role_name = "token-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/staging-token-server-policy"
      }
    }
    www = {
      sa_name   = "www-account"
      role_name = "www-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/www-service-account-policy"
      }
    }
    admin = {
      sa_name   = "admin-account"
      role_name = "admin-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/admin-service-account-policy"
      }
    }
    mgm-ranking-generator = {
      sa_name   = "mgm-ranking-generator-account"
      role_name = "mgm-ranking-generator-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/cronjob-mgm-ranking-generator-policy"
      }
    }
    admin-map = {
      sa_name   = "admin-map-account"
      role_name = "admin-map-assume-role"
      policies = {
        admin_map_policy = "arn:aws:iam::************:policy/admin-map_policy"
      }
    }
    /*
    mbox = {
      sa_name   = "mbox-irsa-account"
      role_name = "mbox-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/mbox-irsa-policy"
      }
    }*/
    status-changer = {
      sa_name   = "status-changer-account"
      role_name = "status-changer-assume-role"
      policies = {
        policy = "arn:aws:iam::************:policy/cronjob-status-changer-policy"
      }
    }
  }
}