resource "aws_security_group" "doc-db-sg" {
  name        = "doc-db-sg"
  description = "Security group for my application"
  vpc_id      = aws_vpc.uniopen-vpc.id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "doc-db-sg"
  }
}

# === Security Group Rules ===

resource "aws_security_group_rule" "cloud9_tcp_27017" {
  type                     = "ingress"
  from_port                = 27017
  to_port                  = 27017
  protocol                 = "tcp"
  security_group_id        = aws_security_group.doc-db-sg.id
  source_security_group_id = "sg-09e4747bcd22c1d1d"
  description              = "cloud9"
}

resource "aws_security_group_rule" "all_traffic_0725859b" {
  type                     = "ingress"
  from_port                = 0
  to_port                  = 0
  protocol                 = "-1"
  security_group_id        = aws_security_group.doc-db-sg.id
  source_security_group_id = "sg-0725859b22c418538"
}

resource "aws_security_group_rule" "tcp_14001_ocd99" {
  type                     = "ingress"
  from_port                = 14001
  to_port                  = 14001
  protocol                 = "tcp"
  security_group_id        = aws_security_group.doc-db-sg.id
  source_security_group_id = "sg-0cd9918593e78b46b"
}

resource "aws_security_group_rule" "tcp_9098_kafka" {
  type                     = "ingress"
  from_port                = 9098
  to_port                  = 9098
  protocol                 = "tcp"
  security_group_id        = aws_security_group.doc-db-sg.id
  source_security_group_id = "sg-065ff73beb8fd069c"
  description              = "kafka"
}

resource "aws_security_group_rule" "tcp_27017_felix" {
  type                     = "ingress"
  from_port                = 27017
  to_port                  = 27017
  protocol                 = "tcp"
  security_group_id        = aws_security_group.doc-db-sg.id
  source_security_group_id = "sg-065ff73beb8fd069c"
  description              = "felix-ssh"
}

resource "aws_security_group_rule" "tcp_27017_jumper" {
  type                     = "ingress"
  from_port                = 27017
  to_port                  = 27017
  protocol                 = "tcp"
  security_group_id        = aws_security_group.doc-db-sg.id
  source_security_group_id = "sg-025712c593365850e"
  description              = "jumper"
}

resource "aws_security_group_rule" "tcp_27017_ocd99" {
  type                     = "ingress"
  from_port                = 27017
  to_port                  = 27017
  protocol                 = "tcp"
  security_group_id        = aws_security_group.doc-db-sg.id
  source_security_group_id = "sg-0cd9918593e78b46b"
}

resource "aws_security_group_rule" "tcp_14002_ocd99" {
  type                     = "ingress"
  from_port                = 14002
  to_port                  = 14002
  protocol                 = "tcp"
  security_group_id        = aws_security_group.doc-db-sg.id
  source_security_group_id = "sg-0cd9918593e78b46b"
}

resource "aws_security_group_rule" "all_traffic_ip" {
  type              = "ingress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  security_group_id = aws_security_group.doc-db-sg.id
  cidr_blocks       = ["*************/32"]
}

resource "aws_security_group_rule" "tcp_27017_0725859b" {
  type                     = "ingress"
  from_port                = 27017
  to_port                  = 27017
  protocol                 = "tcp"
  security_group_id        = aws_security_group.doc-db-sg.id
  source_security_group_id = "sg-0725859b22c418538"
}

resource "aws_security_group_rule" "ssh_10_0_0_0_16" {
  type              = "ingress"
  from_port         = 22
  to_port           = 22
  protocol          = "tcp"
  security_group_id = aws_security_group.doc-db-sg.id
  cidr_blocks       = ["10.0.0.0/16"]
}

resource "aws_security_group_rule" "tcp_9098_kafka_2" {
  type                     = "ingress"
  from_port                = 9098
  to_port                  = 9098
  protocol                 = "tcp"
  security_group_id        = aws_security_group.doc-db-sg.id
  source_security_group_id = "sg-0cd9918593e78b46b"
  description              = "kafka"
}
