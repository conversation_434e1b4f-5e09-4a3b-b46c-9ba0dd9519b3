resource "aws_subnet" "private-ap-northeast-1a" {
  vpc_id            = aws_vpc.uniopen-vpc.id
  cidr_block        = "10.0.0.0/19"
  availability_zone = "ap-northeast-1a"

  tags = {
    "Name"                            = "private-ap-northeast-1a"
    "kubernetes.io/role/internal-elb" = "1"
    "kubernetes.io/cluster/uniopen"      = "owned"
  }
}

resource "aws_subnet" "private-ap-northeast-1c" {
  vpc_id            = aws_vpc.uniopen-vpc.id
  cidr_block        = "*********/19"
  availability_zone = "ap-northeast-1c"

  tags = {
    "Name"                            = "private-ap-northeast-1c"
    "kubernetes.io/role/internal-elb" = "1"
    "kubernetes.io/cluster/uniopen"      = "owned"
  }
}

resource "aws_subnet" "public-ap-northeast-1a" {
  vpc_id                  = aws_vpc.uniopen-vpc.id
  cidr_block              = "*********/19"
  availability_zone       = "ap-northeast-1a"
  map_public_ip_on_launch = true

  tags = {
    "Name"                       = "public-ap-northeast-1a"
    "kubernetes.io/role/elb"     = "1"
    "kubernetes.io/cluster/uniopen" = "owned"
  }
}

resource "aws_subnet" "public-ap-northeast-1c" {
  vpc_id                  = aws_vpc.uniopen-vpc.id
  cidr_block              = "*********/19"
  availability_zone       = "ap-northeast-1c"
  map_public_ip_on_launch = true

  tags = {
    "Name"                       = "public-ap-northeast-1c"
    "kubernetes.io/role/elb"     = "1"
    "kubernetes.io/cluster/uniopen" = "owned"
  }
}